# ++++++++++++++++++++++++++++++++++++++++++
# 定义概念： EditorConfig 包含一个用于定义代码格式的文件（即：.editorconfig）和一组编辑器插件，这些插件是让编辑器读取配置文件并以此来格式化代码
# 参考材料： http://editorconfig.org
# 如何生效： 要使 .editorconfig 在工程中生效，需要在 VSCode 中安装并开启「EditorConfig for vs code」插件
# ++++++++++++++++++++++++++++++++++++++++++

# special property that should be specified at the top of the file outside of any sections
# Set to "true" to stop .editorconfig files search on current file
root = true

# ------------------------------
# [*] 表示匹配所有文件
#
# 类似文件匹配语法：
# 1. [*.{yml,yaml,json}]
# 2. [Makefile]
# 3. [*.md]
# ------------------------------
[*]

# set to "latin1", "utf-8", "utf-8-bom", "utf-16be" or "utf-16le" to control the character set
#字符集utf-8
charset = utf-8

#缩进风格：空格
# set to "tab" or "space" to use hard tabs or soft tabs respectively
indent_style = space

# a whole number defining the number of columns used for each indentation level and the width of soft tabs (when supported)
# When set to "tab", the value of tab_width (if specified) will be used
#缩进大小2
indent_size = 2

# set to "lf", "cr", or "crlf" to control how line breaks are represented
#换行符lf
end_of_line = lf

# set to "true" to ensure file ends with a newline when saving and "false" to ensure it doesn't
#是否在文件的最后插入一个空行
insert_final_newline = true

# set to "true" to remove any whitespace characters preceding newline characters and "false" to ensure it doesn't
#是否删除行尾的空格
trim_trailing_whitespace = true

# 【注】无效配置。EditorConfig 不支持 max_line_length
# max_line_length = 100

# ------------------------------
# [*.md] 表示匹配后缀为 md 的文件
# ------------------------------
# [*.md]
# insert_final_newline = true
# trim_trailing_whitespace = true
