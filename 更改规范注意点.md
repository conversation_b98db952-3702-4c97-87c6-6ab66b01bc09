> [ESLint 开始，说透我如何在团队项目中基于 Vue 做代码校验](https://juejin.cn/post/6974223481181306888#heading-26)


## 注意

* "@antfu/eslint-config": "^0.36.0", 1.几的版本无法eslint教研，跟uni有冲突。

* 1、本地代码提交教研用的还是<span style="color: orange;">husky</span>
， 在你提交的时候会自动帮你格式化你更改的代码，并且解决掉一部分不规范的地方。解决不了的需要手动解决。

* 2、在你<span style="color: orange;">ctr+s</span>保存的时候也会按照规范自动格式化本地代码。

* 3、更改文件前，先<span style="color: orange;">ctr+s</span>保存，自动格式化更改掉一部分代码，后续代码报错在自行解决。

* 4、需要将本地工作区的<span style="color: red;">Prettier禁用掉</span> ，不然的话本地ctr+s保存自动格式化的时候，会与Prettier的格式化规范冲突。

* 5、刚解决一个文件的规范报错问题发现：大部分规范还是好改的，小部分的代码，使用了很多没有定义的变量，或者promise回调中的参数名称相同的，这个需要改完后测试一下。

* 6、建议新拉一个工程，然后分支切换到code-convention-20230630中去修改。保证项目更改无报错、能运行、能打包后在合并到工作分支。

* 7、eslintrc.js中新增三个忽略规范： <span style="color: orange;">'no-alert': 'off', 'no-console': 'off',    camelcase: 'off'</span>,  忽略alert弹窗代码警告。 忽略调试日志打印报错。 忽略驼峰命名法报错。
  * 这三个地方在代码中使用比较多，可以先改完其他的在来优化这一块内容。特别是驼峰命这块，代码大量使用a_b这种命名方法，在新规范中需要使用aB驼峰命名。
  * 新增 <span style="color: orange;">"vue/no-mutating-props": "off"</span>。作用**去除**禁止在 Vue.js 组件中对传递的 prop 进行修改。<span style="color: orange;">"vue/no-mutating-props"</span>这个规则的目的是确保组件内部不会改变从父组件接收到的 prop 值，以遵循单向数据流的原则。去除后可以进行修改。原因是项目中一部分代码使用了在子的props中直接更改父传递过来的引用类型数据，如果不添加去除的话可能涉及到代码逻辑的变更，这个在第一版更改的时候暂不考虑。
  *  <span style="color: orange;">"no-unused-expressions": "off"</span> 关闭，该规则引起的，该规则要求在某些上下文中，表达式必须被分配给变量或作为函数调用。导致 a && b 表达的逻辑代码必须赋值才不会报错。 例如 const c = a && b
  *  <span style="color: orange;">'prefer-destructuring': 'off'</span> 关闭。该规则建议在可能的情况下使用数组解构。导致 const a = array[1]的取值报错。
  *  <span style="color: orange;">'no-nested-ternary': 'off'</span> 关闭。该规则建议不要嵌套使用三元表达式。嵌套的三元表达式可能会导致代码难以理解和维护。导致 三元表达式报错 报错。
  *  <span style="color: orange;">'no-param-reassign': 'off'</span> 关闭。该规则要求不要对函数参数进行重新赋值。当您尝试在函数参数上进行属性赋值时，会触发这个错误。

*  8、根据规范更改注意：
     ```javaScript
     ①、 i++ -> i+=1、 i-- -> i-=1（规范不推荐使用i++, i--写法，以避免造成代码可读性和维护性的问题）

     ②、方法的参数规范不建议直接修改，可以给参数添加一个过渡参数去更改。例如：
     function (params) {
      const a = params
      a.b = c
     }

     ③、要求在使用不同类型的运算符（如 / 和 %）时使用括号明确操作的顺序。例如：const result = (x / y) % z;

     ④、"'v-html' 指令可能导致 XSS 攻击" 错误提示通常是由 ESLint 的规则 "vue/no-v-html" 引起的。解决：
     <!-- eslint-disable vue/no-v-html -->

     ⑤、 ESLint 的 no-restricted-syntax 规则引起的，该规则禁止使用特定的语法结构或语言功能。在这种情况下，它建议不要使用 for...in 循环来遍历对象的属性，因为它会沿着原型链遍历所有可枚举的属性，通常不是我们想要的行为。取而代之的是，可以使用 Object.keys、Object.values 或 Object.entries 方法来获取对象的属性数组，并遍历结果数组。

     ⑥、props: ['data', 'listType', 'index', 'dataListLength', 'paperId', 'applicationType'], 警告报错，可能回到值提交git husky教研出错提交不上去。解决方法忽略警告：
      // eslint-disable-next-line vue/require-prop-types

     ⑦、代码中location.href = fileUrl报错， 更改成：window.location.href = fileUrl;

     ⑧、代码中插槽如果使用了多个<template> 模板标签会报警告：require directive.eslintvue/no-lone-template、  vue/no-lone-template 规则要求 Vue 单文件组件必须包含且只能包含一个 <template> 标签。这是为了确保每个 Vue 单文件组件都有一个明确的模板部分，并且不会有多个模板。
     解决：模板最上方加入 <!-- eslint-disable vue/no-lone-template --> 解决

     ⑨、prefer-rest-params：JavaScript 中使用 rest parameters 替代已废弃的 arguments 对象。Rest 参数提供了一种更现代和灵活的方式来处理可变长度的参数。例如：
      function myFunction() {
         console.log(arguments);
      }
      改为：
      function myFunction(...args) {
         console.log(args);
      }

      ⑩、no-new：它指出不应该在副作用操作中使用 new。在 Vue.js 中创建应用程序实例时使用 new Vue({...}) 是一种副作用操作，因此会触发该规则。根据默认的 no-new 规则，使用 new 关键字进行副作用操作可能会导致代码难以维护和理解。解决："no-new": "off"。 目前不配置此项，单独去忽略解决。需要理解出现"no-new"错误是为什么导致的。
     ```

### 有其他注意的可以再下面补充
* 1. template
    报错 Unexpected useless attribute on `<template>`.eslintvue/no-useless-template-attributes
    <template
    #text
    style="margin-top:10px"
    >
    人数
    </template>
    修改方式 给人数加上span标签，将style设置到span上
* 2. map 没有return
    报错 Array.prototype.map() expects a return value from arrow function.eslint
    举例：
    resFromServer.map((item) => {
      const action = {
        name: item.groupName,
        groupId: item.groupId,
      };
      this.classActionList.push(action);
    });
    修改方式 map 改为forEach

