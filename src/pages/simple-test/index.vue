<template>
  <view class="simple-test">
    <view class="header">
      <text class="title">简化虚拟列表测试</text>
      <text class="info">数据量: {{ testData.length }} | 滚动: {{ scrollTop }}px</text>
    </view>

    <VirtualList
      :data="testData"
      :height="300"
      :item-height="50"
      :buffer-size="3"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      @scroll="onScroll"
    >
      <template #default="{ item, index }">
        <view class="simple-item">
          <text class="item-text">{{ index }}: {{ item.title }}</text>
        </view>
      </template>
    </VirtualList>

    <view class="actions">
      <button @click="addData">添加数据</button>
      <button @click="resetData">重置</button>
      <button @click="clearConsole">清空控制台</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';

const testData = ref<any[]>([]);
const scrollTop = ref(0);

const generateData = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    title: `项目 ${i + 1}`
  }));
};

onMounted(() => {
  testData.value = generateData(50);
});

const onScroll = (event: ScrollEvent) => {
  scrollTop.value = Math.round(event.detail.scrollTop);
};

const addData = () => {
  const newData = generateData(20);
  testData.value.push(
    ...newData.map((item) => ({
      ...item,
      id: testData.value.length + item.id,
      title: `项目 ${testData.value.length + item.id + 1}`
    }))
  );
};

const resetData = () => {
  testData.value = generateData(50);
  scrollTop.value = 0;
};

const clearConsole = () => {
  console.clear();
  uni.showToast({
    title: '控制台已清空',
    icon: 'none'
  });
};
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
}

.info {
  font-size: 14px;
  color: #666;
  display: block;
}

.simple-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.item-text {
  font-size: 16px;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
}
</style>
