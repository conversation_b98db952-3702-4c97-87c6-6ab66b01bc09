<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">虚拟列表测试页面</text>
      <text class="test-desc">测试跨平台兼容性</text>
    </view>

    <!-- 简单测试 -->
    <view class="test-section">
      <text class="section-title">基础功能测试</text>
      <VirtualList
        ref="testListRef"
        :data="testData"
        :height="300"
        :item-height="50"
        :buffer-size="3"
        @scroll="onScroll"
      >
        <template #default="{ item, index }">
          <view class="test-item">
            <text class="item-text">{{ item.title }} - {{ index }}</text>
          </view>
        </template>
      </VirtualList>

      <view class="test-actions">
        <button class="test-btn" @click="scrollToTop">回到顶部</button>
        <button class="test-btn" @click="scrollToBottom">到底部</button>
        <button class="test-btn" @click="addData">添加数据</button>
      </view>
    </view>

    <!-- BasicDemo -->
    <view class="test-section">
      <text class="section-title">BasicDemo测试</text>
      <BasicDemo />
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';
import BasicDemo from '@/components/VirtualList/demos/BasicDemo.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';

// 测试数据
const testData = ref<any[]>([]);
const testListRef = ref<any>(null);

// 生成测试数据
const generateTestData = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    title: `测试项目 ${i + 1}`,
    content: `这是第 ${i + 1} 个测试项目的内容`
  }));
};

// 初始化
onMounted(() => {
  testData.value = generateTestData(100);
});

// 事件处理
const onScroll = (event: ScrollEvent) => {
  console.log('滚动事件:', event.detail.scrollTop);
};

// 操作方法
const scrollToTop = () => {
  if (testListRef.value && testListRef.value.scrollToTop) {
    testListRef.value.scrollToTop();
    uni.showToast({
      title: '已滚动到顶部',
      icon: 'none'
    });
  }
};

const scrollToBottom = () => {
  if (testListRef.value && testListRef.value.scrollToBottom) {
    testListRef.value.scrollToBottom();
    uni.showToast({
      title: '已滚动到底部',
      icon: 'none'
    });
  }
};

const addData = () => {
  const newData = generateTestData(50);
  testData.value.push(...newData);
  uni.showToast({
    title: `已添加 ${newData.length} 项`,
    icon: 'none'
  });
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.test-header {
  margin-bottom: 20px;
  text-align: center;
}

.test-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.test-desc {
  font-size: 14px;
  color: #666;
  display: block;
}

.test-section {
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
}

.test-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.item-text {
  font-size: 14px;
  color: #333;
}

.test-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.test-btn {
  flex: 1;
  padding: 8px 12px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
}

.test-btn:active {
  background: #0056cc;
}
</style>
