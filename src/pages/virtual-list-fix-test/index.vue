<template>
  <view class="fix-test">
    <view class="header">
      <text class="title">虚拟列表修复测试</text>
      <view class="info">
        <text>数据量: {{ testData.length }}</text>
        <text>滚动位置: {{ scrollTop }}px</text>
        <text>平台: {{ platform }}</text>
      </view>
    </view>

    <view class="controls">
      <button @click="addMoreData" class="btn">添加更多数据</button>
      <button @click="scrollToBottom" class="btn">滚动到底部</button>
      <button @click="scrollToTop" class="btn">滚动到顶部</button>
      <button @click="resetData" class="btn">重置数据</button>
    </view>

    <view class="virtual-container">
      <VirtualList
        ref="virtualListRef"
        :data="testData"
        :height="400"
        :item-height="60"
        :buffer-size="8"
        :enhanced="true"
        :bounces="false"
        :show-scrollbar="true"
        @scroll="onScroll"
        @scrollToBottom="onScrollToBottom"
      >
        <template #default="{ item, index }">
          <view class="test-item" :class="{ 'even': index % 2 === 0 }">
            <view class="item-index">{{ index }}</view>
            <view class="item-content">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.description }}</text>
            </view>
            <view class="item-status">
              <text class="status-text">{{ item.status }}</text>
            </view>
          </view>
        </template>
      </VirtualList>
    </view>

    <view class="debug-info">
      <text class="debug-title">调试信息:</text>
      <text class="debug-text">最后滚动时间: {{ lastScrollTime }}</text>
      <text class="debug-text">滚动事件计数: {{ scrollEventCount }}</text>
      <text class="debug-text">渲染项数量: {{ renderedItemCount }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';

const testData = ref<any[]>([]);
const scrollTop = ref(0);
const platform = ref('');
const lastScrollTime = ref('');
const scrollEventCount = ref(0);
const renderedItemCount = ref(0);
const virtualListRef = ref<any>(null);

// 生成测试数据
const generateData = (count: number, startIndex = 0) => {
  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i;
    return {
      id: index,
      title: `测试项目 ${index + 1}`,
      description: `这是第 ${index + 1} 个测试项目的描述信息`,
      status: index % 3 === 0 ? '重要' : index % 3 === 1 ? '普通' : '次要'
    };
  });
};

// 初始化
onMounted(() => {
  // 获取平台信息
  try {
    const systemInfo = uni.getSystemInfoSync();
    platform.value = systemInfo.platform || 'unknown';
  } catch (error) {
    platform.value = 'h5';
  }

  // 初始化数据
  testData.value = generateData(100);
  
  console.log('虚拟列表修复测试页面已加载');
});

// 滚动事件处理
const onScroll = (event: ScrollEvent) => {
  scrollTop.value = Math.round(event.detail.scrollTop);
  scrollEventCount.value++;
  lastScrollTime.value = new Date().toLocaleTimeString();
  
  // 统计当前渲染的项目数量
  const container = document.querySelector('.virtual-list__viewport');
  if (container) {
    renderedItemCount.value = container.children.length;
  }
};

// 滚动到底部事件
const onScrollToBottom = () => {
  console.log('已滚动到底部，可以加载更多数据');
  uni.showToast({
    title: '已到底部',
    icon: 'none'
  });
  
  // 自动加载更多数据
  setTimeout(() => {
    addMoreData();
  }, 500);
};

// 添加更多数据
const addMoreData = () => {
  const currentLength = testData.value.length;
  const newData = generateData(50, currentLength);
  testData.value.push(...newData);
  
  uni.showToast({
    title: `已添加 ${newData.length} 条数据`,
    icon: 'none'
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (virtualListRef.value) {
    virtualListRef.value.scrollToBottom();
  }
};

// 滚动到顶部
const scrollToTop = () => {
  if (virtualListRef.value) {
    virtualListRef.value.scrollToTop();
  }
};

// 重置数据
const resetData = () => {
  testData.value = generateData(100);
  scrollTop.value = 0;
  scrollEventCount.value = 0;
  
  uni.showToast({
    title: '数据已重置',
    icon: 'none'
  });
};
</script>

<style scoped>
.fix-test {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info text {
  font-size: 14px;
  color: #666;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  padding: 8px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
}

.virtual-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
  transition: background-color 0.2s;
}

.test-item.even {
  background: #fafafa;
}

.item-index {
  width: 40px;
  height: 40px;
  background: #007aff;
  color: white;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.item-desc {
  font-size: 14px;
  color: #666;
}

.item-status {
  padding: 4px 8px;
  border-radius: 4px;
  background: #e8f4fd;
}

.status-text {
  font-size: 12px;
  color: #007aff;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.debug-text {
  font-size: 14px;
  color: #666;
}
</style>
