<template>
  <canvas
    id="lottie_canvas"
    type="2d"
    canvas-id="lottie_canvas"
    :class="canvasClass"
    :style="canvasStyle"
  />
</template>

<script setup lang="ts">
// #ifdef MP-WEIXIN
import lottie from 'lottie-miniprogram';
// #endif

interface Props {
  animationData: any;
  width?: string | number;
  height?: string | number;
  loop?: boolean;
  autoplay?: boolean;
  canvasClass?: string;
  animationDataName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  width: '250px',
  height: '120px',
  loop: true,
  autoplay: true,
  canvasClass: '',
  animationDataName: ''
});

const canvasStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    maxWidth: typeof props.width === 'number' ? `${props.width}px` : props.width,
    maxHeight: typeof props.height === 'number' ? `${props.height}px` : props.height
  };
});

let ani: any = null;

// 获取设备像素比
const getDevicePixelRatio = (): number => {
  let dpr = 1;
  // #ifdef MP-WEIXIN
  dpr = uni.getSystemInfoSync().pixelRatio;
  // #endif
  return dpr;
};

// 安全设置canvas尺寸的辅助函数
const setupCanvasSafely = (canvas: any, context: any) => {
  const dpr = getDevicePixelRatio();
  const maxCanvasSize = 8192; // 小程序canvas最大尺寸限制

  // 获取原始尺寸
  const originalWidth = canvas.width;
  const originalHeight = canvas.height;

  // 计算目标尺寸，确保不超过限制
  const targetWidth = Math.min(originalWidth * dpr, maxCanvasSize);
  const targetHeight = Math.min(originalHeight * dpr, maxCanvasSize);

  // 如果计算出的尺寸会超过限制，使用安全的默认值
  const safeWidth =
    originalWidth * dpr > maxCanvasSize
      ? Math.min(parseInt(String(props.width)) * dpr, maxCanvasSize)
      : targetWidth;
  const safeHeight =
    originalHeight * dpr > maxCanvasSize
      ? Math.min(parseInt(String(props.height)) * dpr, maxCanvasSize)
      : targetHeight;

  // 设置canvas尺寸
  canvas.width = safeWidth;
  canvas.height = safeHeight;
  context.scale(dpr, dpr);

  return { width: safeWidth, height: safeHeight };
};
const instance = getCurrentInstance();

// 初始化Lottie动画
const initLottie = () => {
  // #ifdef MP-WEIXIN
  const canvasNode = uni
    .createSelectorQuery()
    .in(instance?.proxy)
    .select('#lottie_canvas');
  console.log('initLottie---canvasNode', canvasNode.node);
  if (canvasNode) {
    canvasNode
      .node((res) => {
        console.log('initLottie---res---1', res);
        if (res && res.node) {
          console.log('initLottie---res---2', res);
          const canvas = res.node;
          console.log('initLottie---canvas', canvas);
          try {
            const context = canvas.getContext('2d');

            // 使用安全的canvas尺寸设置函数
            setupCanvasSafely(canvas, context);

            lottie.setup(canvas);
            ani = lottie.loadAnimation({
              loop: props.loop,
              autoplay: props.autoplay,
              animationData: props.animationData,
              rendererSettings: {
                context
              }
            });
          } catch (error) {
            console.error('Canvas setup error:', error);
          }
        }
      })
      .exec();
  }
  // #endif
};

// 监听动画数据变化，重新加载动画
watch(
  () => props.animationDataName,
  (newVal, oldVal) => {
    console.log('watch---props.animationData-newVal', newVal);
    console.log('watch---props.animationData-oldVal', oldVal);
    console.log('watch---ani', ani);
    if (ani) {
      ani.destroy();
    }
    nextTick(() => {
      console.log('watch---initLottie');
      initLottie();
    });
  },
  { deep: true }
);

onMounted(() => {
  console.log('onMounted---initLottie-1', props.animationData.value);
  console.log('onMounted---initLottie-2', props.animationData);
  initLottie();
});

onBeforeUnmount(() => {
  if (ani) {
    ani.destroy();
    ani = null;
  }
});

// 添加默认导出
defineExpose({
  // 可以在这里暴露一些方法给父组件使用
  destroy: () => {
    if (ani) {
      ani.destroy();
      ani = null;
    }
  },
  play: () => {
    if (ani) {
      ani.play();
    }
  },
  pause: () => {
    if (ani) {
      ani.pause();
    }
  },
  stop: () => {
    if (ani) {
      ani.stop();
    }
  },
  /**
   * 功能：跳转到指定位置并播放
   * 参数：
   * value：目标位置（时间或帧数）
   * isFrame（可选）：是否按帧计算（默认=false按时间）
   */
  goToAndPlay: (value: number, isFrame?: boolean) => {
    if (ani) {
      ani.goToAndPlay(value, isFrame);
    }
  },
  /**
   * 功能：跳转到指定位置并停止
   * 参数：
   * value：目标位置（时间或帧数）
   * isFrame（可选）：是否按帧计算（默认=false按时间）
   */
  goToAndStop: (value: number, isFrame?: boolean) => {
    if (ani) {
      ani.goToAndStop(value, isFrame);
    }
  },
  /**
   * 功能：设置动画速度
   * 参数：
   * value：播放速率（1=正常速度，0.5=半速，2=双倍速）
   */
  setSpeed: (value: number) => {
    if (ani) {
      ani.setSpeed(value);
    }
  },
  /**
   * 功能：设置动画方向
   * 参数：
   * value：方向值（1为正向，-1为反向）
   */
  setDirection: (value: number) => {
    if (ani) {
      ani.setDirection(value);
    }
  },
  /**
   * 功能：播放动画片段
   * 参数：
   * value：片段数组（[开始帧, 结束帧]）或片段数组 [[start1,end1],[start2,end2]]
   * forceFlag（可选）：是否立即播放（true=中断当前，false=等待当前完成）
   */
  playSegments: (value: number[], forceFlag?: boolean) => {
    if (ani) {
      ani.playSegments(value, forceFlag);
    }
  },
  /**
   * 功能：设置动画子帧
   * 参数：
   * value：true=平滑渲染（默认），false=整数帧渲染
   */
  setSubframe: (value: boolean) => {
    if (ani) {
      ani.setSubframe(value);
    }
  },
  /**
   * 功能：获取动画时长
   * 参数：
   * inFrames（可选）：是否按帧计算（默认=false按时间）
   */
  getDuration: () => {
    if (ani) {
      return ani.getDuration();
    }
  }
});
</script>

<style lang="scss" scoped>
canvas {
  display: block;
}
</style>

<script lang="ts">
export default {
  name: 'XxtLottie'
};
</script>
