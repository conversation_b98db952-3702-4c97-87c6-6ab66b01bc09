<template>
  <view class="mp-test-container">
    <view class="test-header">
      <text class="test-title">小程序虚拟列表测试</text>
      <text class="test-desc">专门针对小程序端优化的测试页面</text>
    </view>

    <!-- 测试信息 -->
    <view class="test-info">
      <text class="info-item">平台: {{ platformInfo }}</text>
      <text class="info-item">数据量: {{ testData.length }}</text>
      <text class="info-item">滚动位置: {{ scrollPosition }}px</text>
    </view>

    <!-- 虚拟列表 -->
    <VirtualList
      ref="virtualListRef"
      :data="testData"
      :height="listHeight"
      :item-height="itemHeight"
      :buffer-size="bufferSize"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      @scroll="onScroll"
      @item-visible="onItemVisible"
      @item-hidden="onItemHidden"
    >
      <template #default="{ item, index, isVisible }">
        <view class="test-item" :class="{ 'item-visible': isVisible }">
          <view class="item-index">{{ index + 1 }}</view>
          <view class="item-content">
            <text class="item-title">{{ item.title }}</text>
            <text class="item-desc">{{ item.description }}</text>
          </view>
          <view class="item-status">
            <text class="status-text">{{ isVisible ? '可见' : '隐藏' }}</text>
          </view>
        </view>
      </template>
    </VirtualList>

    <!-- 操作按钮 -->
    <view class="test-actions">
      <button class="action-btn" @click="scrollToTop">回到顶部</button>
      <button class="action-btn" @click="scrollToBottom">到底部</button>
      <button class="action-btn" @click="addData">添加数据</button>
      <button class="action-btn" @click="resetData">重置数据</button>
    </view>

    <!-- 调试信息 -->
    <view class="debug-info">
      <text class="debug-title">调试信息:</text>
      <text class="debug-item">可见项: {{ visibleCount }}</text>
      <text class="debug-item">渲染项: {{ renderCount }}</text>
      <text class="debug-item">滚动状态: {{ isScrolling ? '滚动中' : '静止' }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';

// 基础配置
const listHeight = ref(400);
const itemHeight = ref(60);
const bufferSize = ref(5); // 减少缓冲区大小，避免渲染过多项目

// 数据和状态
const testData = ref<any[]>([]);
const scrollPosition = ref(0);
const visibleCount = ref(0);
const renderCount = ref(0);
const isScrolling = ref(false);

// 组件引用
const virtualListRef = ref<any>(null);

// 平台信息
const platformInfo = ref('');

// 生成测试数据
const generateTestData = (count: number, startIndex = 0) => {
  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i;
    return {
      id: index,
      title: `测试项目 ${index + 1}`,
      description: `这是第 ${index + 1} 个测试项目的详细描述信息，用于测试虚拟列表的渲染效果。`,
      type: ['normal', 'important', 'urgent'][index % 3],
      timestamp: Date.now() - Math.random() * 86400000
    };
  });
};

// 初始化
onMounted(() => {
  // 获取平台信息
  try {
    const systemInfo = uni.getSystemInfoSync();
    platformInfo.value = `${systemInfo.platform} - ${systemInfo.version}`;
  } catch (error) {
    platformInfo.value = 'Unknown';
  }

  // 生成初始数据
  testData.value = generateTestData(200);
});

// 事件处理
const onScroll = (event: ScrollEvent) => {
  scrollPosition.value = Math.round(event.detail.scrollTop);

  // 简单的滚动状态检测
  isScrolling.value = true;
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  scrollTimer = setTimeout(() => {
    isScrolling.value = false;
  }, 150) as unknown as number;
};

let scrollTimer: number | null = null;

const onItemVisible = (index: number, item: any) => {
  console.log(`项目 ${index} 变为可见:`, item.title);
  visibleCount.value++;
};

const onItemHidden = (index: number, item: any) => {
  console.log(`项目 ${index} 变为隐藏:`, item.title);
  visibleCount.value = Math.max(0, visibleCount.value - 1);
};

// 操作方法
const scrollToTop = () => {
  if (virtualListRef.value && virtualListRef.value.scrollToTop) {
    virtualListRef.value.scrollToTop();
    uni.showToast({
      title: '已滚动到顶部',
      icon: 'none'
    });
  }
};

const scrollToBottom = () => {
  if (virtualListRef.value && virtualListRef.value.scrollToBottom) {
    virtualListRef.value.scrollToBottom();
    uni.showToast({
      title: '已滚动到底部',
      icon: 'none'
    });
  }
};

const addData = () => {
  const newData = generateTestData(50, testData.value.length);
  testData.value.push(...newData);
  uni.showToast({
    title: `已添加 ${newData.length} 项`,
    icon: 'none'
  });
};

const resetData = () => {
  testData.value = generateTestData(200);
  scrollPosition.value = 0;
  visibleCount.value = 0;
  uni.showToast({
    title: '数据已重置',
    icon: 'none'
  });
};

// 计算属性
const renderCountComputed = computed(() => {
  return Math.min(
    Math.ceil(listHeight.value / itemHeight.value) + bufferSize.value * 2,
    testData.value.length
  );
});

renderCount.value = renderCountComputed.value;
</script>

<style scoped>
.mp-test-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  margin-bottom: 15px;
  text-align: center;
}

.test-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.test-desc {
  font-size: 14px;
  color: #666;
  display: block;
}

.test-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  font-size: 14px;
  color: #333;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-bottom: 1px solid #eee;
  transition: all 0.3s;
  opacity: 0.7;
}

.test-item.item-visible {
  opacity: 1;
  background: #f8f9ff;
}

.item-index {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #007aff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.item-content {
  flex: 1;
  margin-left: 15px;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.item-desc {
  font-size: 14px;
  color: #666;
  display: block;
  line-height: 1.4;
}

.item-status {
  padding: 4px 8px;
  border-radius: 12px;
  background: #e8f5e8;
}

.status-text {
  font-size: 12px;
  color: #4cd964;
}

.test-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 15px 0;
}

.action-btn {
  flex: 1;
  min-width: 80px;
  padding: 10px 15px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

.action-btn:active {
  background: #0056cc;
}

.debug-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.debug-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.debug-item {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 5px;
}
</style>
