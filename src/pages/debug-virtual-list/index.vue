<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">虚拟列表调试页面</text>
      <text class="debug-info">数据量: {{ testData.length }}</text>
    </view>

    <view class="debug-controls">
      <button class="debug-btn" @click="toggleDataSize">
        切换数据量 (当前: {{ testData.length }})
      </button>
      <button class="debug-btn" @click="logState">输出状态</button>
    </view>

    <view class="virtual-container">
      <VirtualList
        ref="virtualListRef"
        :data="testData"
        :height="300"
        :item-height="60"
        :buffer-size="5"
        :enhanced="true"
        :bounces="false"
        :show-scrollbar="true"
        @scroll="onScroll"
      >
        <template #default="{ item, index }">
          <view class="debug-item">
            <view class="item-index">{{ index }}</view>
            <view class="item-content">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-id">ID: {{ item.id }}</text>
            </view>
          </view>
        </template>
      </VirtualList>
    </view>

    <view class="debug-log">
      <text class="log-title">调试日志:</text>
      <view class="log-content">
        <text v-for="(log, index) in debugLogs" :key="index" class="log-item">
          {{ log }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';

const testData = ref<any[]>([]);
const virtualListRef = ref<any>(null);
const debugLogs = ref<string[]>([]);

// 添加调试日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  debugLogs.value.unshift(`[${timestamp}] ${message}`);
  if (debugLogs.value.length > 10) {
    debugLogs.value = debugLogs.value.slice(0, 10);
  }
  console.log(`[VirtualList Debug] ${message}`);
};

// 生成测试数据
const generateData = (count: number) => {
  addLog(`生成 ${count} 条测试数据`);
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: `测试项目 ${i + 1}`,
    description: `这是第 ${i + 1} 个测试项目`
  }));
};

// 切换数据量
const toggleDataSize = () => {
  const currentSize = testData.value.length;
  let newSize;

  if (currentSize <= 5) {
    newSize = 20;
  } else if (currentSize <= 20) {
    newSize = 100;
  } else {
    newSize = 5;
  }

  testData.value = generateData(newSize);
  addLog(`数据量从 ${currentSize} 切换到 ${newSize}`);
};

// 输出当前状态
const logState = () => {
  addLog(`当前数据量: ${testData.value.length}`);
  addLog(`虚拟列表引用: ${virtualListRef.value ? '已获取' : '未获取'}`);

  // 输出详细状态到控制台
  console.log('=== 虚拟列表状态 ===');
  console.log('数据:', testData.value);
  console.log('虚拟列表引用:', virtualListRef.value);
  console.log('==================');
};

// 滚动事件
const onScroll = (event: ScrollEvent) => {
  // 减少滚动日志的频率
  if (Math.random() < 0.1) {
    addLog(`滚动位置: ${Math.round(event.detail.scrollTop)}px`);
  }
};

// 初始化
onMounted(() => {
  addLog('调试页面已挂载');
  testData.value = generateData(20);

  // 延迟输出状态，确保组件完全初始化
  setTimeout(() => {
    logState();
  }, 1000);
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  margin-bottom: 20px;
  text-align: center;
}

.debug-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.debug-info {
  font-size: 14px;
  color: #666;
}

.debug-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.debug-btn {
  padding: 8px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
}

.virtual-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.debug-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.item-index {
  width: 30px;
  height: 30px;
  background: #007aff;
  color: white;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 15px;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.item-id {
  font-size: 12px;
  color: #666;
}

.debug-log {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  font-size: 12px;
  color: #666;
  padding: 5px;
  background: #f8f8f8;
  border-radius: 4px;
  font-family: monospace;
}
</style>
