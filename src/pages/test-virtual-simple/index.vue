<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">虚拟列表简单测试</text>
      <text class="test-info">数据量: {{ testData.length }}</text>
    </view>

    <view class="virtual-container">
      <VirtualList
        :data="testData"
        :height="300"
        :item-height="50"
        :buffer-size="5"
        @scroll="onScroll"
      >
        <template #default="{ item, index }">
          <view class="test-item">
            <text class="item-text">{{ index }}: {{ item.title }}</text>
          </view>
        </template>
      </VirtualList>
    </view>

    <view class="test-controls">
      <button @click="addData" class="test-btn">添加数据</button>
      <button @click="clearData" class="test-btn">清空数据</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';

const testData = ref<any[]>([]);

const generateData = (count: number, startIndex = 0) => {
  return Array.from({ length: count }, (_, i) => ({
    id: startIndex + i,
    title: `测试项目 ${startIndex + i + 1}`
  }));
};

const onScroll = (event: any) => {
  console.log('滚动位置:', event.detail.scrollTop);
};

const addData = () => {
  const newData = generateData(10, testData.value.length);
  testData.value.push(...newData);
  console.log('添加数据后总数:', testData.value.length);
};

const clearData = () => {
  testData.value = [];
  console.log('数据已清空');
};

onMounted(() => {
  console.log('简单测试页面挂载');
  testData.value = generateData(20);
  console.log('初始数据:', testData.value);
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  margin-bottom: 20px;
  text-align: center;
}

.test-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.test-info {
  font-size: 14px;
  color: #666;
}

.virtual-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.test-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.item-text {
  font-size: 16px;
  color: #333;
}

.test-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.test-btn {
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
}
</style>
