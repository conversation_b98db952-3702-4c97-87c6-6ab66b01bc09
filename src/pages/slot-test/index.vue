<template>
  <view class="slot-test">
    <view class="header">
      <text class="title">Slot问题测试页面</text>
      <text class="desc">专门测试slot重复警告问题</text>
    </view>

    <!-- 测试信息 -->
    <view class="test-info">
      <text class="info-text">请打开控制台查看是否有slot警告</text>
      <text class="info-text">数据量: {{ testData.length }}</text>
    </view>

    <!-- 最小化的虚拟列表测试 -->
    <VirtualList
      :data="testData"
      :height="200"
      :item-height="40"
      :buffer-size="2"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
    >
      <template #default="{ item, index }">
        <view class="test-item">
          <text>{{ index }}: {{ item.name }}</text>
        </view>
      </template>
    </VirtualList>

    <view class="actions">
      <button @click="addItems">添加5项</button>
      <button @click="removeItems">删除5项</button>
      <button @click="resetItems">重置</button>
    </view>

    <!-- 控制台清理 -->
    <view class="console-actions">
      <button @click="clearConsole">清空控制台</button>
      <button @click="logCurrentItems">输出当前项</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';

const testData = ref<any[]>([]);

const generateItems = (count: number, startIndex = 0) => {
  return Array.from({ length: count }, (_, i) => ({
    id: startIndex + i,
    name: `测试项 ${startIndex + i + 1}`
  }));
};

onMounted(() => {
  testData.value = generateItems(20);
});

const addItems = () => {
  const newItems = generateItems(5, testData.value.length);
  testData.value.push(...newItems);
  console.log('添加了5个项目，当前总数:', testData.value.length);
};

const removeItems = () => {
  if (testData.value.length > 5) {
    testData.value.splice(-5, 5);
    console.log('删除了5个项目，当前总数:', testData.value.length);
  }
};

const resetItems = () => {
  testData.value = generateItems(20);
  console.log('重置为20个项目');
};

const clearConsole = () => {
  console.clear();
  uni.showToast({
    title: '控制台已清空',
    icon: 'none'
  });
};

const logCurrentItems = () => {
  console.log('当前项目列表:', testData.value);
};
</script>

<style scoped>
.slot-test {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.desc {
  font-size: 14px;
  color: #666;
  display: block;
}

.test-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.info-text {
  font-size: 14px;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.test-item {
  padding: 10px 15px;
  background: white;
  border-bottom: 1px solid #eee;
}

.actions,
.console-actions {
  display: flex;
  gap: 10px;
  margin: 15px 0;
}

button {
  flex: 1;
  padding: 10px 15px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

button:active {
  background: #0056cc;
}

.console-actions button {
  background: #34c759;
}

.console-actions button:active {
  background: #28a745;
}
</style>
