<template>
  <!-- <xxt-layout tabbar="/pages/template-index/template-index"> -->
  <view class="content tui-skeleton">
    <image class="logo tui-skeleton-rect" src="/static/logo.png" />
    <view class="text-area tui-skeleton-rect">
      <text class="title tui-skeleton-rect">{{ title }}</text>
    </view>
    <button @click="goToGameMap">游戏闯关地图</button>
    <button @click="goToVirtual">虚拟列表Demo</button>
    <button @click="goToVirtual2">虚拟列表virtualDemo</button>
    <button @click="goToVirtual3">虚拟列表mp-test</button>
    <button @click="goToVirtual4">虚拟列表simple-test</button>
    <button @click="goToVirtual5">虚拟列表slot-test</button>
    <!-- #ifdef MP-WEIXIN -->
    <view class="lottie-container">
      <XxtLottie
        ref="lottieRef"
        :animation-data="currentLottieData"
        :animation-data-name="currentLottieDataName"
        :width="250"
        :height="120"
        canvas-id="lottie_canvas_main"
      />
      <view class="lottie-container">
        <text>动画时长：{{ duration }}</text>
      </view>
    </view>
    <view class="button-group">
      <button @click="changeLottie('jump')">跳跃动画</button>
      <button @click="changeLottie('tiger')">老虎动画</button>
      <button @click="changeLottie('owl')">猫头鹰动画</button>
      <button @click="changeLottie('chicken')">小鸡动画</button>
      <button @click="changeLottie('cloudyRain')">多云有雨动画</button>
      <button @click="changeLottie('lightRain')">小雨动画</button>
      <button @click="changeLottie('error')">错误动画</button>
      <button @click="changeLottie('clap')">鼓掌动画</button>
    </view>
    <view class="button-group operate">
      <button @click="playLottie">动画播放</button>
      <button @click="pauseLottie">动画暂停</button>
      <button @click="stopLottie">动画停止</button>
      <button @click="goToAndPlayLottie">跳转到指定位置并播放</button>
      <button @click="goToAndStopLottie">跳转到指定位置并暂停</button>
      <!-- 设置动画速度 -->
      <button @click="setSpeedLottie">设置动画速度</button>
      <!-- 设置动画方向 -->
      <button @click="setDirectionLottie">设置动画方向</button>
      <button @click="playSegmentsLottie">动画播放片段</button>
      <button @click="setSubframeLottie">设置动画子帧</button>
      <button @click="destroyLottie">销毁动画</button>
      <button @click="getDurationLottie">获取动画时长</button>
    </view>
    <!-- #endif -->
  </view>
  <xxt-skeleton :skeleton-show="skeletonShow" :is-list="false"></xxt-skeleton>
  <!-- </xxt-layout> -->
</template>

<script setup lang="ts">
// #ifdef MP-WEIXIN
import XxtLottie from '../components/XxtLottie.vue';
import tigerJson from './tiger.json';
import owlJson from './owl.json';
import jumpJson from './jump.json';
import chickenJson from './chicken.json';
import cloudyRainJson from './cloudyRain.json';
import lightRainJson from './lightRain.json';
import errorJson from './error.json';
import clapJson from './clapHands.json';
// #endif

const title = ref('Hello');
interface testtype {
  a: number;
  [key: string]: any;
}
const haha = 12;
const a1 = '10';
const props: testtype = {
  a: 1,
  b: 12
};

console.log(props, a1, haha);
const skeletonShow = ref(true);
const duration = ref(0);

// #ifdef MP-WEIXIN
// 当前选择的动画数据
const currentLottieData = ref<any>(jumpJson);
const currentLottieDataName = ref<string>('jump');
// 切换不同的Lottie动画
const changeLottie = (type: string) => {
  switch (type) {
    case 'jump':
      currentLottieData.value = jumpJson;
      currentLottieDataName.value = 'jump';
      break;
    case 'tiger':
      currentLottieData.value = tigerJson;
      currentLottieDataName.value = 'tiger';
      break;
    case 'owl':
      currentLottieData.value = owlJson;
      currentLottieDataName.value = 'owl';
      break;
    case 'chicken':
      currentLottieData.value = chickenJson;
      currentLottieDataName.value = 'chicken';
      break;
    case 'cloudyRain':
      currentLottieData.value = cloudyRainJson;
      currentLottieDataName.value = 'cloudyRain';
      break;
    case 'lightRain':
      currentLottieData.value = lightRainJson;
      currentLottieDataName.value = 'lightRain';
      break;
    case 'error':
      currentLottieData.value = errorJson;
      currentLottieDataName.value = 'error';
      break;
    case 'clap':
      currentLottieData.value = clapJson;
      currentLottieDataName.value = 'clap';
      break;
    default:
      currentLottieData.value = jumpJson;
      currentLottieDataName.value = 'jump';
  }
};
const lottieRef = ref<any>(null);
const playLottie = () => {
  lottieRef.value.play();
};
const pauseLottie = () => {
  lottieRef.value.pause();
};
const stopLottie = () => {
  lottieRef.value.stop();
};
const goToAndPlayLottie = () => {
  lottieRef.value.goToAndPlay(10, true);
};
const goToAndStopLottie = () => {
  lottieRef.value.goToAndStop(20, true);
};
const setSpeedLottie = () => {
  lottieRef.value.setSpeed(0.5);
};
const setDirectionLottie = () => {
  lottieRef.value.setDirection(-1);
};
const playSegmentsLottie = () => {
  lottieRef.value.playSegments([10, 20], false);
};
const setSubframeLottie = () => {
  lottieRef.value.setSubframe(true);
};
const destroyLottie = () => {
  lottieRef.value.destroy();
};
const getDurationLottie = () => {
  duration.value = lottieRef.value.getDuration();
};
// #endif
const router = useRouter();
const goToGameMap = () => {
  router.push({
    path: '/pages/game-map/index'
  });
};
const goToVirtual = () => {
  router.push({
    path: '/components/VirtualList/demos/index'
  });
};
const goToVirtual2 = () => {
  router.push({
    path: '/pages/game-map/virtualDemo'
  });
};
const goToVirtual3 = () => {
  router.push({
    path: '/pages/mp-test/index'
  });
};
const goToVirtual4 = () => {
  router.push({
    path: '/pages/simple-test/index'
  });
};
const goToVirtual5 = () => {
  router.push({
    path: 'pages/slot-test/index'
  });
};
onLoad(() => {
  // 模拟
  setTimeout(() => {
    skeletonShow.value = false;
  }, 2000);
});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.logo {
  margin-left: auto;
  margin-right: auto;
  margin-top: 200rpx;
  margin-bottom: 50rpx;
  width: 200rpx;
  height: 200rpx;
}
.text-area {
  display: flex;
  justify-content: center;
}
.title {
  font-size: 36rpx;
  color: #8f8f94;
}
.lottie-container {
  margin: 20rpx 0;
}
.button-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 20rpx;
  button {
    margin: 10rpx;
    font-size: 28rpx;
  }
}
.operate {
  margin-top: 20rpx;
  background-color: #f0f0f0;
}
</style>
