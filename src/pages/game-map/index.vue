<template>
  <tui-navigation-bar background-color="#78d782">
    <view class="bar" :style="{ opacity: 1 }">
      <view class="tui-header-icon" @click="goBack">
        <tui-icon name="arrowleft" color="#222" size="28"></tui-icon>
      </view>
      <view class="tui-header-title">游戏闯关地图</view>
    </view>
  </tui-navigation-bar>
  <view class="container">
    <VirtualList
      :data="mapList"
      :height="400"
      :item-height="220"
      :buffer-size="5"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="true"
      class="map-virtual-list"
      @scroll="handleScroll"
      @scroll-to-top="handleScrollToTop"
      @scroll-to-bottom="handleScrollToBottom"
      @item-visible="handleItemVisible"
      @item-hidden="handleItemHidden"
    >
      <template #default="{ item, index, isVisible }">
        <view
          class="map-item"
          :class="{ 'map-item--visible': isVisible }"
          :style="{ height: `${mapItemHeight}px` }"
        >
          <view class="map-content" :style="{ backgroundColor: item.color }">
            <view class="map-title">{{ item.title }}</view>
            <view class="map-level">第{{ index + 1 }}关</view>

            <!-- 图片加载成功时显示 -->
            <image
              v-if="item.imageLoaded && !item.imageError"
              :src="item.image"
              class="map-image map-image--loaded"
              mode="aspectFill"
              @load="handleImageLoad(item, index)"
              @error="handleImageError(item, index)"
            />

            <!-- 图片加载中时显示 -->
            <view
              v-else-if="item.imageLoading"
              class="map-image-placeholder map-image-placeholder--loading"
            >
              <view class="loading-spinner"></view>
              <text class="placeholder-text">图片加载中...</text>

              <!-- 加载进度条 -->
              <view v-if="item.loadProgress !== undefined" class="progress-container">
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: `${item.loadProgress}%` }"></view>
                </view>
                <text class="progress-text">{{ Math.round(item.loadProgress) }}%</text>
              </view>
            </view>

            <!-- 图片加载失败时显示 -->
            <view
              v-else-if="item.imageError"
              class="map-image-placeholder map-image-placeholder--error"
            >
              <view class="error-icon">⚠️</view>
              <text class="placeholder-text">图片加载失败</text>
              <view class="retry-container">
                <text class="retry-text" @tap="retryLoadImage(item, index)">点击重试</text>
              </view>
            </view>

            <!-- 默认占位符 -->
            <view v-else class="map-image-placeholder">
              <view class="placeholder-icon">🖼️</view>
              <text class="placeholder-text">等待加载...</text>
            </view>

            <!-- 图片尺寸信息（开发模式显示） -->
            <view v-if="item.imageLoaded && item.imageSize" class="image-info">
              <text class="size-text">{{ item.imageSize.width }}×{{ item.imageSize.height }}</text>
            </view>
          </view>
        </view>
      </template>

      <template #empty>
        <view class="map-empty">
          <text class="empty-text">暂无地图数据</text>
        </view>
      </template>

      <template #loading>
        <view class="map-loading">
          <text class="loading-text">地图加载中...</text>
        </view>
      </template>
    </VirtualList>

    <!-- 开发模式：图片加载统计面板 -->
    <!-- #ifdef APP-PLUS || H5 -->
    <view v-if="showDebugPanel" class="debug-panel">
      <view class="debug-header" @tap="toggleDebugPanel">
        <text class="debug-title">图片加载统计</text>
        <text class="debug-toggle">{{ debugPanelExpanded ? '▼' : '▶' }}</text>
      </view>

      <view v-if="debugPanelExpanded" class="debug-content">
        <view class="debug-row">
          <text class="debug-label">总图片:</text>
          <text class="debug-value">{{ imageStats.totalImages }}</text>
        </view>
        <view class="debug-row">
          <text class="debug-label">已加载:</text>
          <text class="debug-value">{{ imageStats.loadedImages }}</text>
        </view>
        <view class="debug-row">
          <text class="debug-label">加载中:</text>
          <text class="debug-value">{{ imageStats.loadingImages }}</text>
        </view>
        <view class="debug-row">
          <text class="debug-label">加载失败:</text>
          <text class="debug-value">{{ imageStats.errorImages }}</text>
        </view>

        <view class="debug-actions">
          <text class="debug-btn" @tap="preloadAllImages">预加载全部</text>
          <text class="debug-btn" @tap="clearImageCache(false)">清理缓存</text>
          <text class="debug-btn" @tap="clearImageCache(true)">强制清理</text>
        </view>
      </view>
    </view>
    <!-- #endif -->
  </view>
</template>

<script setup lang="ts">
import VirtualList from '@/components/VirtualList/index.vue';
import type { ScrollEvent } from '@/components/VirtualList/types';
import {
  type ImageLoadResult,
  imageLoader
} from '@/components/VirtualList/utils/image-lazy-loader';

const router = useRouter();

// 地图数据接口
interface MapItem {
  id: number;
  title: string;
  image: string;
  color: string;
  imageLoaded: boolean;
  imageError: boolean;
  imageLoading: boolean;
  imageSize?: { width: number; height: number };
  loadProgress?: number;
}

// 地图数据 - 扩展更多地图项以测试虚拟列表性能
const mapList = ref<MapItem[]>([
  {
    id: 1,
    title: '森林迷宫',
    image: 'https://img.yzcdn.cn/vant/apple-1.jpg',
    color: '#4CAF50',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 2,
    title: '火山熔岩',
    image: 'https://img.yzcdn.cn/vant/apple-2.jpg',
    color: '#FF5722',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 3,
    title: '冰雪王国',
    image: 'https://img.yzcdn.cn/vant/apple-3.jpg',
    color: '#2196F3',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 4,
    title: '沙漠绿洲',
    image: 'https://img.yzcdn.cn/vant/apple-4.jpg',
    color: '#FF9800',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 5,
    title: '深海探险',
    image: 'https://img.yzcdn.cn/vant/apple-1.jpg',
    color: '#009688',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 6,
    title: '天空之城',
    image: 'https://img.yzcdn.cn/vant/apple-2.jpg',
    color: '#9C27B0',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 7,
    title: '地下城堡',
    image: 'https://img.yzcdn.cn/vant/apple-3.jpg',
    color: '#795548',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 8,
    title: '魔法森林',
    image: 'https://img.yzcdn.cn/vant/apple-4.jpg',
    color: '#8BC34A',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 9,
    title: '星空战场',
    image: 'https://img.yzcdn.cn/vant/apple-1.jpg',
    color: '#3F51B5',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  },
  {
    id: 10,
    title: '终极BOSS',
    image: 'https://img.yzcdn.cn/vant/apple-2.jpg',
    color: '#E91E63',
    imageLoaded: false,
    imageError: false,
    imageLoading: false
  }
]);

// 图片加载统计
const imageStats = ref({
  totalImages: 0,
  loadedImages: 0,
  errorImages: 0,
  loadingImages: 0
});

// 虚拟列表配置
const listHeight = ref('100vh');
const mapItemHeight = ref(220); // 每个地图项的高度 (200px + 20px padding)

// 更新图片统计信息
const updateImageStats = () => {
  imageStats.value = {
    totalImages: mapList.value.length,
    loadedImages: mapList.value.filter((item) => item.imageLoaded).length,
    errorImages: mapList.value.filter((item) => item.imageError).length,
    loadingImages: mapList.value.filter((item) => item.imageLoading).length
  };
};

// 批量预加载图片 - 使用智能优先级
const preloadImages = async (items: MapItem[], priority: 'high' | 'normal' | 'low' = 'low') => {
  const imagesToPreload = items
    .filter((item) => item.image && !item.imageLoaded && !item.imageLoading)
    .map((item) => item.image);

  if (imagesToPreload.length > 0) {
    console.log(`预加载 ${imagesToPreload.length} 张图片，优先级: ${priority}`);
    await imageLoader.preloadImages(imagesToPreload, priority);
  }
};

// 获取系统信息来调整列表高度
onMounted(() => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const navigationBarHeight = 44; // 导航栏高度
    const availableHeight = systemInfo.windowHeight - statusBarHeight - navigationBarHeight;
    listHeight.value = `${availableHeight}px`;
  } catch (error) {
    console.warn('获取系统信息失败，使用默认高度', error);
    listHeight.value = 'calc(100vh - 44px)';
  }

  // 配置图片加载器
  imageLoader.setMaxConcurrentLoads(3); // 最大并发3个
  imageLoader.setMaxCacheSize(50); // 最大缓存50张图片

  // 延迟初始化，确保所有函数都已定义
  nextTick(() => {
    // 初始化图片统计
    updateImageStats();

    // 预加载前几张图片以提升初始体验
    setTimeout(() => {
      const initialItems = mapList.value.slice(0, 3);
      preloadImages(initialItems, 'high');
    }, 500);
  });
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理缓存以释放内存
  imageLoader.clearCache(true);
  console.log('游戏地图页面已卸载，图片缓存已清理');
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 滚动事件处理
const handleScroll = (event: ScrollEvent) => {
  // 可以在这里添加滚动相关的业务逻辑
  console.log('地图列表滚动:', event.detail.scrollTop);
};

// 滚动到顶部事件
const handleScrollToTop = () => {
  console.log('滚动到地图列表顶部');
  // 可以添加到顶部的业务逻辑，比如刷新数据
};

// 滚动到底部事件
const handleScrollToBottom = () => {
  console.log('滚动到地图列表底部');
  // 可以添加到底部的业务逻辑，比如加载更多地图
};

// 图片懒加载逻辑 - 使用增强的图片加载器
const loadImageLazily = async (
  item: MapItem,
  index: number,
  priority: 'high' | 'normal' | 'low' = 'normal'
) => {
  if (!item.image || item.imageLoaded || item.imageLoading) {
    return;
  }

  // 检查缓存
  const cached = imageLoader.getCachedImage(item.image);
  if (cached) {
    if (cached.loaded) {
      item.imageLoaded = true;
      item.imageError = false;
      item.imageLoading = false;
      item.imageSize = cached.size;
      updateImageStats();
      return;
    } else if (cached.error && cached.retryCount >= 3) {
      item.imageError = true;
      item.imageLoaded = false;
      item.imageLoading = false;
      updateImageStats();
      return;
    }
  }

  // 开始加载
  item.imageLoading = true;
  item.imageError = false;
  item.loadProgress = 0;
  updateImageStats();

  try {
    console.log(`开始加载地图 ${index} 图片:`, item.title, `优先级: ${priority}`);

    // 模拟加载进度
    const progressTimer = setInterval(() => {
      if (item.loadProgress !== undefined && item.loadProgress < 90) {
        item.loadProgress += Math.random() * 20;
      }
    }, 200);

    const result: ImageLoadResult = await imageLoader.loadImage({
      src: item.image,
      timeout: 15000, // 15秒超时
      retryCount: 3,
      retryDelay: 1000,
      priority
    });

    clearInterval(progressTimer);

    if (result.success) {
      // 图片加载成功
      item.imageLoaded = true;
      item.imageError = false;
      item.imageLoading = false;
      item.imageSize = result.size;
      item.loadProgress = 100;

      console.log(
        `地图 ${index} 图片加载成功:`,
        item.title,
        result.fromCache ? '(来自缓存)' : '(新加载)'
      );
    } else {
      // 图片加载失败
      item.imageError = true;
      item.imageLoaded = false;
      item.imageLoading = false;
      item.loadProgress = 0;

      console.error(`地图 ${index} 图片加载失败:`, item.title, result.error);
    }
  } catch (error) {
    // 异常处理
    item.imageError = true;
    item.imageLoaded = false;
    item.imageLoading = false;
    item.loadProgress = 0;

    console.error(`地图 ${index} 图片加载异常:`, item.title, error);
  } finally {
    updateImageStats();
  }
};

// 这些函数已经在上面定义了，移除重复定义

// 地图项可见事件 - 优化的图片加载策略
const handleItemVisible = async (index: number, item: MapItem) => {
  console.log(`地图项 ${index} 变为可见:`, item.title);

  // 立即高优先级加载当前可见项的图片
  await loadImageLazily(item, index, 'high');

  // 智能预加载策略：预加载附近的图片
  const preloadRange = 2; // 预加载前后2个项目
  const startIndex = Math.max(0, index - preloadRange);
  const endIndex = Math.min(mapList.value.length, index + preloadRange + 1);

  const nearbyItems = mapList.value.slice(startIndex, endIndex);

  // 分批预加载：当前项目附近的用normal优先级，更远的用low优先级
  const immediateItems = nearbyItems.slice(0, 3); // 最近的3个
  const distantItems = nearbyItems.slice(3); // 更远的项目

  if (immediateItems.length > 0) {
    preloadImages(immediateItems, 'normal');
  }

  if (distantItems.length > 0) {
    // 延迟预加载更远的项目，避免影响当前加载
    setTimeout(() => {
      preloadImages(distantItems, 'low');
    }, 500);
  }
};

// 地图项隐藏事件 - 优化内存管理
const handleItemHidden = (index: number, item: MapItem) => {
  console.log(`地图项 ${index} 变为隐藏:`, item.title);

  // 重置加载进度
  if (item.loadProgress !== undefined) {
    item.loadProgress = 0;
  }

  // 定期清理缓存以避免内存泄漏
  if (index % 10 === 0) {
    // 每隐藏10个项目清理一次
    setTimeout(() => {
      imageLoader.clearCache();
      updateImageStats();
    }, 1000);
  }
};

// 图片加载成功处理 - DOM层面的加载确认
const handleImageLoad = (item: MapItem, index: number) => {
  console.log(`地图 ${index} 图片DOM加载成功:`, item.title);

  // 确保状态同步
  if (!item.imageLoaded) {
    item.imageLoaded = true;
    item.imageError = false;
    item.imageLoading = false;
    item.loadProgress = 100;
    updateImageStats();
  }
};

// 图片加载失败处理 - DOM层面的错误处理
const handleImageError = (item: MapItem, index: number) => {
  console.error(`地图 ${index} 图片DOM加载失败:`, item.title);

  item.imageError = true;
  item.imageLoaded = false;
  item.imageLoading = false;
  item.loadProgress = 0;
  updateImageStats();

  // 智能重试机制：延迟后重新尝试加载
  const retryDelay = 2000 + Math.random() * 3000; // 2-5秒随机延迟
  setTimeout(() => {
    if (item.imageError && !item.imageLoaded) {
      console.log(`尝试重新加载地图 ${index} 图片:`, item.title);
      loadImageLazily(item, index, 'normal');
    }
  }, retryDelay);
};

// 手动重试加载图片
const retryLoadImage = async (item: MapItem, index: number) => {
  console.log(`手动重试加载地图 ${index} 图片:`, item.title);

  // 重置状态
  item.imageError = false;
  item.imageLoaded = false;
  item.loadProgress = 0;

  // 高优先级重新加载
  await loadImageLazily(item, index, 'high');
};

// 清理图片缓存 - 增强版
const clearImageCache = (force = false) => {
  imageLoader.clearCache(force);

  // 重置所有地图项的状态
  if (force) {
    mapList.value.forEach((item) => {
      item.imageLoaded = false;
      item.imageError = false;
      item.imageLoading = false;
      item.loadProgress = 0;
      item.imageSize = undefined;
    });
  }

  updateImageStats();
  console.log('图片缓存已清理', force ? '(强制清理)' : '(智能清理)');
};

// getCacheStats函数已移除，避免未使用警告

// 预加载所有图片 - 用于初始化或用户主动触发
const preloadAllImages = async () => {
  console.log('开始预加载所有地图图片...');

  const allImages = mapList.value.map((item) => item.image);
  await imageLoader.preloadImages(allImages, 'normal');

  console.log('所有地图图片预加载完成');
  updateImageStats();
};

// 调试面板相关
const showDebugPanel = ref(false);
const debugPanelExpanded = ref(false);

// 切换调试面板
const toggleDebugPanel = () => {
  debugPanelExpanded.value = !debugPanelExpanded.value;
};

// 开发模式下显示调试面板
// #ifdef APP-PLUS || H5
showDebugPanel.value = true;
// #endif
</script>

<style scoped lang="scss">
.container {
  @include normalContainer();
  min-height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 虚拟列表容器 */
.map-virtual-list {
  width: 100%;
  height: 100%;
}

/* 地图项容器 */
.map-item {
  width: 100%;
  height: 220px;
  padding: 10px;
  box-sizing: border-box;
  transition: opacity 0.3s ease;

  &--visible {
    opacity: 1;
  }

  &:not(.map-item--visible) {
    opacity: 0.7;
  }
}

/* 地图内容 */
.map-content {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 地图标题 */
.map-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
  z-index: 2;
}

/* 地图关卡 */
.map-level {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

/* 地图图片 */
.map-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
  transition: opacity 0.3s ease;

  &--loading {
    opacity: 0.1;
  }
}

/* 图片占位符 */
.map-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  gap: 8px;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  text-align: center;
}

.placeholder-icon {
  font-size: 24px;
  opacity: 0.6;
}

/* 加载状态样式 */
.map-image-placeholder--loading {
  background: rgba(255, 255, 255, 0.15);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 进度条样式 */
.progress-container {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 错误状态样式 */
.map-image-placeholder--error {
  background: rgba(255, 87, 34, 0.1);
}

.error-icon {
  font-size: 32px;
  opacity: 0.8;
}

.retry-container {
  margin-top: 8px;
}

.retry-text {
  color: #fff;
  font-size: 14px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.retry-text:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 图片信息显示 */
.image-info {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  padding: 2px 6px;
  z-index: 3;
}

.size-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
}

/* 空状态样式 */
.map-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.empty-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

/* 加载状态样式 */
.map-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loading-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

/* 导航栏样式 */
.bar {
  position: fixed;
  z-index: 3;
  width: 100%;
  height: 44px;
  line-height: 44px;
  vertical-align: middle;
  text-align: center;
}

.tui-header-icon {
  position: absolute;
  left: 2px;
  padding: 0 10px 6px 0;
}

.tui-header-title {
  display: inline-block;
  color: #222;
  font-weight: 600;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
  .map-item {
    height: 200px;
  }

  .map-title {
    font-size: 20px;
  }

  .map-level {
    font-size: 14px;
  }
}

/* 调试面板样式 */
.debug-panel {
  position: fixed;
  top: 100px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 8px;
  z-index: 1000;
  min-width: 150px;
  backdrop-filter: blur(10px);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
}

.debug-title {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.debug-toggle {
  color: #fff;
  font-size: 12px;
}

.debug-content {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.debug-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

.debug-value {
  color: #4caf50;
  font-size: 11px;
  font-weight: bold;
}

.debug-actions {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.debug-btn {
  color: #fff;
  font-size: 10px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.debug-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 性能优化 */
.map-content {
  will-change: transform;
  backface-visibility: hidden;
}

.map-image {
  will-change: opacity;
}
</style>
