@font-face {
  font-family: "iconfont"; /* Project id 42600 */
  src: url('iconfont.eot?t=1702624144964'); /* IE9 */
  src: url('iconfont.eot?t=1702624144964#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1702624144964') format('woff2'),
       url('iconfont.woff?t=1702624144964') format('woff'),
       url('iconfont.ttf?t=1702624144964') format('truetype'),
       url('iconfont.svg?t=1702624144964#iconfont') format('svg');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-x-add:before {
  content: '\e623';
}

.icon-x-shipin:before {
  content: '\e700';
}

.icon-x-cuowu:before {
  content: '\e777';
}

.icon-x-gonggulianxi:before {
  content: '\e855';
}

.icon-x-chahao:before {
  content: '\e6a2';
}

.icon-x-tianjiafujian:before {
  content: '\e6f4';
}

.icon-x-shipin3:before {
  content: '\e6fb';
}

.icon-x-tupian1:before {
  content: '\e6f9';
}

.icon-x-lianjie1:before {
  content: '\e6f5';
}

.icon-x-yinpin:before {
  content: '\e6f7';
}

.icon-x-wenjian2:before {
  content: '\e6f6';
}

.icon-x-shanchu:before {
  content: "\e7c7";
}
