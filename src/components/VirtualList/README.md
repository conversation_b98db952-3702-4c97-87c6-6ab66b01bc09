# VirtualList 虚拟列表组件

高性能的虚拟列表组件，专门针对uni-app多端环境优化。通过只渲染可视区域内的元素来解决大量列表项的性能问题，特别适用于游戏地图等图片密集场景。

## 特性

- 🚀 **高性能**：只渲染可视区域内的元素，支持万级数据流畅滚动
- 📱 **多端兼容**：支持微信小程序、App端、H5端，保持一致的API和体验
- 🎨 **高度可定制**：支持插槽自定义列表项内容，灵活适应各种UI需求
- 💪 **TypeScript**：完整的类型定义支持，提供优秀的开发体验
- 🔧 **智能优化**：内置平台特定优化策略，自动适配最佳性能配置
- 🛡️ **错误处理**：完善的错误处理和降级机制，保证组件稳定性
- 🔄 **缓冲机制**：智能缓冲区管理，减少滚动时的白屏现象

## 快速开始

### 安装

将 `VirtualList` 组件复制到你的项目中：

```
src/components/VirtualList/
├── index.vue                 # 主组件文件
├── types.ts                  # TypeScript类型定义
├── utils/                    # 工具类
├── hooks/                    # 组合式API
└── README.md                 # 使用文档
```

### 基本用法

```vue
<template>
  <VirtualList
    :data="listData"
    :height="600"
    :item-height="80"
    @scroll="handleScroll"
  >
    <template #default="{ item, index }">
      <view class="list-item">
        <text>{{ item.title }}</text>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
import VirtualList from '@/components/VirtualList'
import type { ScrollEvent } from '@/components/VirtualList/types'

const listData = ref([
  { id: 1, title: 'Item 1' },
  { id: 2, title: 'Item 2' },
  // ... more items
])

const handleScroll = (event: ScrollEvent) => {
  console.log('Scroll position:', event.detail.scrollTop)
}
</script>
```

## API 文档

### Props

| 属性            | 类型             | 默认值 | 必填 | 说明                                                       |
| --------------- | ---------------- | ------ | ---- | ---------------------------------------------------------- |
| data            | Array            | []     | 是   | 列表数据源，支持任意类型的数组数据                         |
| height          | string \| number | -      | 是   | 容器高度，可以是数字（px）或字符串（如 '100vh', '500rpx'） |
| width           | string \| number | '100%' | 否   | 容器宽度，可以是数字（px）或字符串                         |
| itemHeight      | number           | 50     | 否   | 列表项高度（px），固定高度模式下使用                       |
| dynamicHeight   | boolean          | false  | 否   | 是否支持动态高度，开启后会根据内容自动计算高度             |
| bufferSize      | number           | 5      | 否   | 缓冲区大小，在可视区域外预渲染的元素数量                   |
| threshold       | number           | 0      | 否   | 滚动阈值，触发滚动事件的最小滚动距离                       |
| scrollTop       | number           | 0      | 否   | 初始滚动位置                                               |
| enableBackToTop | boolean          | false  | 否   | 是否启用回到顶部功能                                       |
| enhanced        | boolean          | false  | 否   | 是否启用增强模式（小程序端专用，提升滚动性能）             |
| bounces         | boolean          | true   | 否   | 是否启用回弹效果                                           |
| showScrollbar   | boolean          | true   | 否   | 是否显示滚动条                                             |

### Events

| 事件名         | 参数                     | 说明                                           |
| -------------- | ------------------------ | ---------------------------------------------- |
| scroll         | event: ScrollEvent       | 滚动时触发，包含滚动位置、滚动方向等信息       |
| scrollToTop    | -                        | 滚动到顶部时触发                               |
| scrollToBottom | -                        | 滚动到底部时触发                               |
| itemVisible    | index: number, item: any | 列表项进入可视区域时触发，可用于懒加载等场景   |
| itemHidden     | index: number, item: any | 列表项离开可视区域时触发，可用于资源清理等场景 |

#### ScrollEvent 类型定义

```typescript
interface ScrollEvent {
  detail: {
    scrollTop: number;    // 垂直滚动位置
    scrollLeft: number;   // 水平滚动位置
    scrollHeight: number; // 内容总高度
    scrollWidth: number;  // 内容总宽度
    deltaX: number;       // 水平滚动增量
    deltaY: number;       // 垂直滚动增量
  };
}
```

### Slots

| 插槽名  | 参数                                             | 说明                                                                       |
| ------- | ------------------------------------------------ | -------------------------------------------------------------------------- |
| default | { item: any, index: number, isVisible: boolean } | 列表项内容插槽，item为当前数据项，index为索引，isVisible表示是否在可视区域 |
| empty   | -                                                | 空状态内容插槽，当数据为空时显示                                           |
| loading | -                                                | 加载状态内容插槽，可用于显示加载动画                                       |

## 使用示例

### 基础列表

```vue
<template>
  <VirtualList
    :data="basicList"
    :height="400"
    :item-height="60"
  >
    <template #default="{ item, index }">
      <view class="basic-item">
        <text class="title">{{ item.title }}</text>
        <text class="subtitle">{{ item.subtitle }}</text>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
const basicList = ref(
  Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    title: `标题 ${i + 1}`,
    subtitle: `副标题 ${i + 1}`
  }))
)
</script>

<style scoped>
.basic-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
}
.title {
  font-size: 16px;
  font-weight: bold;
}
.subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>
```

### 图片列表（游戏地图场景）

```vue
<template>
  <VirtualList
    :data="gameMapList"
    height="100vh"
    :item-height="200"
    :buffer-size="3"
    @item-visible="onMapVisible"
  >
    <template #default="{ item, index, isVisible }">
      <view class="map-item">
        <image
          v-if="isVisible"
          :src="item.imageUrl"
          class="map-image"
          mode="aspectFill"
          :lazy-load="true"
        />
        <view class="map-info">
          <text class="map-name">{{ item.name }}</text>
          <text class="map-level">等级: {{ item.level }}</text>
        </view>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
const gameMapList = ref([
  {
    id: 1,
    name: '新手村',
    level: '1-10',
    imageUrl: '/static/maps/newbie.jpg'
  },
  {
    id: 2,
    name: '森林迷宫',
    level: '10-20',
    imageUrl: '/static/maps/forest.jpg'
  }
  // ... 更多地图数据
])

const onMapVisible = (index: number, item: any) => {
  console.log(`地图 ${item.name} 进入可视区域`)
  // 可以在这里预加载相关资源
}
</script>

<style scoped>
.map-item {
  position: relative;
  height: 200px;
  margin-bottom: 10px;
}
.map-image {
  width: 100%;
  height: 160px;
  border-radius: 8px;
}
.map-info {
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 0 0 8px 8px;
}
.map-name {
  color: white;
  font-size: 16px;
  font-weight: bold;
}
.map-level {
  color: #ccc;
  font-size: 14px;
}
</style>
```

### 动态高度列表

```vue
<template>
  <VirtualList
    :data="dynamicList"
    :height="500"
    :item-height="80"
    :dynamic-height="true"
  >
    <template #default="{ item }">
      <view class="dynamic-item">
        <text class="title">{{ item.title }}</text>
        <text class="content">{{ item.content }}</text>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
const dynamicList = ref([
  {
    id: 1,
    title: '短内容',
    content: '这是一个短内容。'
  },
  {
    id: 2,
    title: '长内容',
    content: '这是一个很长很长的内容，包含了大量的文字信息，用于测试动态高度的功能是否正常工作。'
  }
  // ... 更多数据
])
</script>

<style scoped>
.dynamic-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
}
.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}
.content {
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}
</style>
```

### 空状态和加载状态

```vue
<template>
  <VirtualList
    :data="dataList"
    :height="400"
    :item-height="60"
  >
    <template #default="{ item }">
      <view class="list-item">
        {{ item.name }}
      </view>
    </template>

    <template #empty>
      <view class="empty-state">
        <text>暂无数据</text>
      </view>
    </template>

    <template #loading>
      <view class="loading-state">
        <text>加载中...</text>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
const dataList = ref([])
const loading = ref(true)

// 模拟数据加载
setTimeout(() => {
  dataList.value = [
    { id: 1, name: '项目1' },
    { id: 2, name: '项目2' }
  ]
  loading.value = false
}, 2000)
</script>

<style scoped>
.empty-state, .loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}
</style>
```

### 高级配置示例

```vue
<template>
  <VirtualList
    :data="advancedList"
    :height="600"
    width="100%"
    :item-height="100"
    :buffer-size="10"
    :threshold="50"
    :enhanced="true"
    :bounces="false"
    :show-scrollbar="false"
    @scroll="onScroll"
    @scroll-to-top="onScrollToTop"
    @scroll-to-bottom="onScrollToBottom"
    @item-visible="onItemVisible"
    @item-hidden="onItemHidden"
  >
    <template #default="{ item, index, isVisible }">
      <view class="advanced-item" :class="{ visible: isVisible }">
        <view class="item-index">{{ index + 1 }}</view>
        <view class="item-content">
          <text class="item-title">{{ item.title }}</text>
          <text class="item-desc">{{ item.description }}</text>
        </view>
        <view class="item-status" :class="item.status">
          {{ item.status }}
        </view>
      </view>
    </template>
  </VirtualList>
</template>

<script setup lang="ts">
import type { ScrollEvent } from '@/components/VirtualList/types'

const advancedList = ref(
  Array.from({ length: 5000 }, (_, i) => ({
    id: i,
    title: `高级项目 ${i + 1}`,
    description: `这是第 ${i + 1} 个项目的详细描述`,
    status: ['active', 'inactive', 'pending'][i % 3]
  }))
)

const onScroll = (event: ScrollEvent) => {
  console.log('滚动位置:', event.detail.scrollTop)
}

const onScrollToTop = () => {
  console.log('已滚动到顶部')
}

const onScrollToBottom = () => {
  console.log('已滚动到底部')
  // 可以在这里加载更多数据
}

const onItemVisible = (index: number, item: any) => {
  console.log(`项目 ${item.title} 进入可视区域`)
}

const onItemHidden = (index: number, item: any) => {
  console.log(`项目 ${item.title} 离开可视区域`)
}
</script>

<style scoped>
.advanced-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
  transition: opacity 0.3s;
  opacity: 0.7;
}

.advanced-item.visible {
  opacity: 1;
}

.item-index {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007aff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.item-content {
  flex: 1;
  margin-left: 15px;
}

.item-title {
  font-size: 16px;
  font-weight: bold;
  display: block;
}

.item-desc {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
  display: block;
}

.item-status {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.item-status.active {
  background: #4cd964;
  color: white;
}

.item-status.inactive {
  background: #ff3b30;
  color: white;
}

.item-status.pending {
  background: #ff9500;
  color: white;
}
</style>
```

## 最佳实践

### 性能优化建议

1. **合理设置缓冲区大小**
   ```javascript
   // 对于简单列表项，缓冲区可以小一些
   const bufferSize = 5

   // 对于复杂列表项（如包含图片），缓冲区应该大一些
   const bufferSize = 10
   ```

2. **使用 isVisible 控制资源加载**
   ```vue
   <template #default="{ item, isVisible }">
     <view class="item">
       <!-- 只在可视时加载图片 -->
       <image v-if="isVisible" :src="item.imageUrl" />
       <!-- 或使用懒加载 -->
       <image :src="item.imageUrl" :lazy-load="true" />
     </view>
   </template>
   ```

3. **避免在插槽中进行复杂计算**
   ```vue
   <!-- 不推荐：在模板中进行复杂计算 -->
   <template #default="{ item }">
     <view>{{ complexCalculation(item) }}</view>
   </template>

   <!-- 推荐：预先计算好数据 -->
   <template #default="{ item }">
     <view>{{ item.computedValue }}</view>
   </template>
   ```

4. **合理使用事件监听**
   ```javascript
   // 避免在 scroll 事件中进行频繁操作
   const onScroll = throttle((event) => {
     // 处理滚动逻辑
   }, 100)
   ```

### 平台特定优化

#### 小程序端
```vue
<VirtualList
  :enhanced="true"
  :bounces="false"
  :show-scrollbar="false"
>
```

#### App端
```vue
<VirtualList
  :bounces="true"
  :buffer-size="8"
>
```

#### H5端
```vue
<VirtualList
  :show-scrollbar="true"
  :threshold="30"
>
```

### 数据处理建议

1. **数据预处理**
   ```javascript
   // 为数据添加唯一标识
   const processedData = rawData.map((item, index) => ({
     ...item,
     _virtualId: item.id || index,
     _virtualIndex: index
   }))
   ```

2. **大数据集处理**
   ```javascript
   // 对于超大数据集，考虑分页加载
   const pageSize = 1000
   const loadPage = (page) => {
     const start = page * pageSize
     const end = start + pageSize
     return allData.slice(start, end)
   }
   ```

### 常见问题解决

#### 1. 滚动位置不准确
```javascript
// 确保 itemHeight 设置正确
const itemHeight = 80 // 必须与实际渲染高度一致

// 或使用动态高度
const dynamicHeight = true
```

#### 2. 图片加载导致高度变化
```vue
<template #default="{ item, isVisible }">
  <view class="item" :style="{ height: `${itemHeight  }px` }">
    <image
      v-if="isVisible"
      :src="item.imageUrl"
      :style="{ height: '100px' }" // 固定图片高度
    />
  </view>
</template>
```

#### 3. 滚动性能问题
```javascript
// 减少缓冲区大小
const bufferSize = 3

// 增加滚动阈值
const threshold = 50

// 使用节流
const scrollHandler = throttle(handleScroll, 16) // 60fps
```

## 错误处理

组件内置了完善的错误处理机制：

### 数据验证和规范化

- **空数据处理**：自动处理 `null`、`undefined` 和空数组
- **数据类型转换**：自动将非数组数据转换为数组格式
- **数据规范化**：为每个数据项添加必要的虚拟化属性
- **循环引用检测**：检测并处理对象循环引用问题

### 配置验证和修复

- **参数验证**：验证所有配置参数的有效性
- **自动修复**：自动修复无效的配置参数
- **边界值处理**：处理超出合理范围的参数值
- **默认值应用**：为缺失的必需参数提供默认值

### 边界情况处理

- **小数据集优化**：少于10项时自动禁用虚拟化
- **大数据集优化**：超过10000项时自动调整缓冲区和节流参数
- **容器尺寸适配**：根据容器和项目高度关系优化配置
- **性能监控**：监控性能问题并自动优化

### 降级策略

- **虚拟化降级**：在不支持虚拟化时降级到普通列表
- **平台兼容性**：处理平台特定的兼容性问题
- **错误恢复**：在出现错误时提供可用的降级方案

### 错误类型

```typescript
// 数据错误
class DataError extends VirtualListError {
  // 数据格式错误、空数据、循环引用等
}

// 配置错误
class ConfigError extends VirtualListError {
  // 无效配置、参数超出范围等
}

// 平台错误
class PlatformError extends VirtualListError {
  // 平台兼容性问题
}

// 性能错误
class PerformanceError extends VirtualListError {
  // 性能问题、内存溢出等
}
```

### 使用示例

```vue
<template>
  <VirtualList
    :data="problematicData"
    :height="invalidHeight"
    :item-height="invalidItemHeight"
  >
    <template #default="{ item, index }">
      <div>{{ item.name || item.value || item }}</div>
    </template>
  </VirtualList>
</template>

<script setup>
// 组件会自动处理这些问题：
const problematicData = [
  null,                    // 会被过滤掉
  undefined,              // 会被过滤掉
  'string item',          // 会被规范化
  { name: 'Object item' }, // 正常处理
  42                      // 会被规范化
];

const invalidHeight = -100;     // 会被修复为正值
const invalidItemHeight = 0;    // 会被设置为默认值
</script>
```

## 常见问题解答 (FAQ)

### Q: 什么时候应该使用虚拟列表？
A: 当你的列表包含超过100个项目，或者列表项包含复杂内容（如图片、视频）时，建议使用虚拟列表来提升性能。

### Q: 虚拟列表支持哪些平台？
A: 支持uni-app的所有平台：
- 微信小程序
- App端（iOS/Android）
- H5端
- 其他小程序平台（支付宝、百度、字节跳动等）

### Q: 如何处理不同高度的列表项？
A: 设置 `dynamic-height="true"` 启用动态高度模式：
```vue
<VirtualList
  :data="data"
  :height="400"
  :item-height="80"
  :dynamic-height="true"
>
```

### Q: 为什么滚动时会出现白屏？
A: 可能的原因和解决方案：
1. **缓冲区太小**：增加 `buffer-size` 的值
2. **列表项渲染太慢**：简化列表项内容，避免复杂计算
3. **图片加载慢**：使用 `lazy-load` 或在 `isVisible` 为 true 时才加载图片

### Q: 如何实现下拉刷新和上拉加载？
A: 监听 `scroll-to-top` 和 `scroll-to-bottom` 事件：
```vue
<VirtualList
  @scroll-to-top="onRefresh"
  @scroll-to-bottom="onLoadMore"
>
```

### Q: 组件的性能如何？
A: 性能表现：
- **小数据集（<100项）**：与普通列表性能相当
- **中等数据集（100-1000项）**：性能提升明显，滚动更流畅
- **大数据集（>1000项）**：性能提升显著，内存使用稳定

### Q: 如何调试性能问题？
A: 使用浏览器开发者工具：
1. **Performance 面板**：分析渲染性能
2. **Memory 面板**：监控内存使用
3. **Console**：查看组件内部日志（开发模式下）

### Q: 支持横向滚动吗？
A: 当前版本主要支持垂直滚动。如需横向滚动，可以通过CSS样式实现：
```css
.virtual-list-container {
  overflow-x: auto;
  white-space: nowrap;
}
```

### Q: 如何自定义滚动条样式？
A: 通过CSS自定义：
```css
/* H5端 */
.virtual-list-container::-webkit-scrollbar {
  width: 6px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}
```

### Q: 组件是否支持TypeScript？
A: 完全支持TypeScript，提供完整的类型定义：
```typescript
import type { VirtualListProps, ScrollEvent } from '@/components/VirtualList/types'
```

### Q: 如何处理数据更新？
A: 组件会自动响应数据变化：
```javascript
// 添加数据
data.value.push(newItem)

// 删除数据
data.value.splice(index, 1)

// 替换数据
data.value = newData
```

### Q: 在小程序中使用时有什么注意事项？
A: 小程序特殊配置：
1. 启用 `enhanced` 模式提升性能
2. 注意图片域名白名单配置
3. 避免使用过多的自定义组件嵌套

### Q: 如何实现搜索功能？
A: 通过过滤数据实现：
```javascript
const filteredData = computed(() => {
  return originalData.value.filter(item =>
    item.title.includes(searchKeyword.value)
  )
})
```

### Q: 组件的兼容性如何？
A: 兼容性要求：
- **uni-app**: 3.0+
- **Vue**: 3.0+
- **TypeScript**: 4.0+（可选）

### Q: 如何贡献代码或报告问题？
A: 欢迎通过以下方式参与：
1. 提交Issue报告问题
2. 提交Pull Request贡献代码
3. 完善文档和示例

## 更新日志

### v1.0.0 (开发中)
- ✅ 基础虚拟列表功能
- ✅ 多端平台适配
- ✅ TypeScript支持
- ✅ 错误处理机制
- ✅ 性能优化策略
- ⏳ 单元测试
- ⏳ 文档完善

## 许可证

MIT License

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看本文档的FAQ部分
2. 查看组件源码中的注释
3. 参考示例代码
4. 提交Issue获取帮助
