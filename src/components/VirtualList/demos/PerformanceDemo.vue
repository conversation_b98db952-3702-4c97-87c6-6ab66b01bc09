<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">性能对比演示</text>
      <text class="demo-desc">对比虚拟列表与普通列表的性能差异</text>
    </view>

    <!-- 测试配置 -->
    <view class="test-config">
      <view class="config-row">
        <text class="config-label">数据量:</text>
        <picker :range="dataSizeOptions" :value="dataSizeIndex" @change="onDataSizeChange">
          <view class="picker-value">{{ dataSizeOptions[dataSizeIndex] }}</view>
        </picker>
      </view>
      <view class="config-row">
        <text class="config-label">项目复杂度:</text>
        <picker :range="complexityOptions" :value="complexityIndex" @change="onComplexityChange">
          <view class="picker-value">{{ complexityOptions[complexityIndex] }}</view>
        </picker>
      </view>
    </view>

    <!-- 性能指标 -->
    <view class="performance-metrics">
      <view class="metric-section">
        <text class="section-title">虚拟列表</text>
        <view class="metrics-grid">
          <view class="metric-card">
            <text class="metric-value">{{ virtualMetrics.renderTime }}ms</text>
            <text class="metric-label">渲染时间</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ virtualMetrics.memoryUsage }}MB</text>
            <text class="metric-label">内存使用</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ virtualMetrics.fps }}</text>
            <text class="metric-label">FPS</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ virtualMetrics.domNodes }}</text>
            <text class="metric-label">DOM节点</text>
          </view>
        </view>
      </view>

      <view class="metric-section">
        <text class="section-title">普通列表</text>
        <view class="metrics-grid">
          <view class="metric-card">
            <text class="metric-value">{{ normalMetrics.renderTime }}ms</text>
            <text class="metric-label">渲染时间</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ normalMetrics.memoryUsage }}MB</text>
            <text class="metric-label">内存使用</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ normalMetrics.fps }}</text>
            <text class="metric-label">FPS</text>
          </view>
          <view class="metric-card">
            <text class="metric-value">{{ normalMetrics.domNodes }}</text>
            <text class="metric-label">DOM节点</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 性能对比图表 -->
    <view class="performance-chart">
      <text class="chart-title">性能提升对比</text>
      <view class="chart-bars">
        <view class="chart-item">
          <text class="chart-label">渲染时间</text>
          <view class="chart-bar-container">
            <view
              class="chart-bar virtual"
              :style="{ width: getBarWidth('renderTime', 'virtual') }"
            ></view>
            <view
              class="chart-bar normal"
              :style="{ width: getBarWidth('renderTime', 'normal') }"
            ></view>
          </view>
          <text class="chart-improvement">{{ getImprovement('renderTime') }}</text>
        </view>

        <view class="chart-item">
          <text class="chart-label">内存使用</text>
          <view class="chart-bar-container">
            <view
              class="chart-bar virtual"
              :style="{ width: getBarWidth('memoryUsage', 'virtual') }"
            ></view>
            <view
              class="chart-bar normal"
              :style="{ width: getBarWidth('memoryUsage', 'normal') }"
            ></view>
          </view>
          <text class="chart-improvement">{{ getImprovement('memoryUsage') }}</text>
        </view>

        <view class="chart-item">
          <text class="chart-label">DOM节点</text>
          <view class="chart-bar-container">
            <view
              class="chart-bar virtual"
              :style="{ width: getBarWidth('domNodes', 'virtual') }"
            ></view>
            <view
              class="chart-bar normal"
              :style="{ width: getBarWidth('domNodes', 'normal') }"
            ></view>
          </view>
          <text class="chart-improvement">{{ getImprovement('domNodes') }}</text>
        </view>
      </view>
    </view>

    <!-- 测试按钮 -->
    <view class="test-controls">
      <button class="test-btn primary" :disabled="testing" @click="runPerformanceTest">
        {{ testing ? '测试中...' : '开始性能测试' }}
      </button>
      <button class="test-btn secondary" @click="clearResults">清空结果</button>
    </view>

    <!-- 列表展示区域 -->
    <view class="list-container">
      <view class="list-section">
        <text class="list-title">虚拟列表 ({{ testData.length }} 项)</text>
        <VirtualList
          v-if="showVirtualList"
          ref="virtualListRef"
          :data="testData"
          :height="300"
          :item-height="itemHeight"
          :buffer-size="5"
          @scroll="onVirtualScroll"
        >
          <template #default="{ item, index }">
            <view class="test-item" :class="complexity">
              <view class="item-header">
                <view class="item-avatar" :style="{ background: item?.color || '#ccc' }">
                  {{ item?.name?.charAt(0) || '?' }}
                </view>
                <view class="item-info">
                  <text class="item-name">{{ item?.name || '' }}</text>
                  <text class="item-desc">{{ item?.description || '' }}</text>
                </view>
                <text class="item-index">#{{ index + 1 }}</text>
              </view>

              <view v-if="complexity !== 'simple'" class="item-content">
                <text class="content-text">{{ item?.content || '' }}</text>
                <view v-if="complexity === 'complex'" class="item-tags">
                  <text v-for="tag in item?.tags || []" :key="tag" class="tag">{{ tag }}</text>
                </view>
              </view>

              <view class="item-footer">
                <text class="item-time">{{ item?.time || '' }}</text>
                <text class="item-status" :class="item?.status || 'default'">{{
                  item?.status || ''
                }}</text>
              </view>
            </view>
          </template>
        </VirtualList>
      </view>

      <view class="list-section">
        <text class="list-title">普通列表 ({{ Math.min(testData.length, 1000) }} 项显示)</text>
        <scroll-view
          v-if="showNormalList"
          class="normal-list"
          scroll-y
          :style="{ height: '300px' }"
          @scroll="onNormalScroll"
        >
          <view
            v-for="(item, index) in normalListData"
            :key="item?.id || index"
            class="test-item"
            :class="complexity"
          >
            <view class="item-header">
              <view class="item-avatar" :style="{ background: item?.color || '#ccc' }">
                {{ item?.name?.charAt(0) || '?' }}
              </view>
              <view class="item-info">
                <text class="item-name">{{ item?.name || '' }}</text>
                <text class="item-desc">{{ item?.description || '' }}</text>
              </view>
              <text class="item-index">#{{ index + 1 }}</text>
            </view>

            <view v-if="complexity !== 'simple'" class="item-content">
              <text class="content-text">{{ item?.content || '' }}</text>
              <view v-if="complexity === 'complex'" class="item-tags">
                <text v-for="tag in item?.tags || []" :key="tag" class="tag">{{ tag }}</text>
              </view>
            </view>

            <view class="item-footer">
              <text class="item-time">{{ item?.time || '' }}</text>
              <text class="item-status" :class="item?.status || 'default'">{{
                item?.status || ''
              }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 测试日志 -->
    <view v-if="testLog.length" class="test-log">
      <text class="log-title">测试日志</text>
      <scroll-view class="log-content" scroll-y>
        <text v-for="(log, index) in testLog" :key="index" class="log-item" :class="log.type">
          [{{ log.time }}] {{ log.message }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import VirtualList from '../index.vue';
import type { ScrollEvent } from '../types';

// 配置选项
const dataSizeOptions = ['100', '500', '1000', '2000', '5000', '10000'];
const dataSizeIndex = ref(2);
const complexityOptions = ['简单', '中等', '复杂'];
const complexityIndex = ref(1);

// 测试数据
const testData = ref<any[]>([]);
const showVirtualList = ref(true);
const showNormalList = ref(true);
const testing = ref(false);

// 性能指标
const virtualMetrics = reactive({
  renderTime: 0,
  memoryUsage: 0,
  fps: 60,
  domNodes: 0
});

const normalMetrics = reactive({
  renderTime: 0,
  memoryUsage: 0,
  fps: 60,
  domNodes: 0
});

// 测试日志
const testLog = ref<any[]>([]);

// 计算属性
const dataSize = computed(() => parseInt(dataSizeOptions[dataSizeIndex.value]));
const complexity = computed(() => {
  const complexityMap = ['simple', 'medium', 'complex'];
  return complexityMap[complexityIndex.value];
});

const itemHeight = computed(() => {
  const heightMap = { simple: 60, medium: 80, complex: 120 };
  return heightMap[complexity.value as keyof typeof heightMap];
});

const normalListData = computed(() => {
  // 普通列表显示前1000项，
  return testData.value.slice(0, Math.min(1000, testData.value.length));
});

// 数据生成
const generateTestData = () => {
  const size = dataSize.value;
  const comp = complexity.value;

  addLog('info', `开始生成 ${size} 条 ${complexityOptions[complexityIndex.value]} 数据`);

  const colors = ['#007aff', '#4cd964', '#ff9500', '#ff3b30', '#5856d6', '#8e44ad'];
  const statuses = ['active', 'inactive', 'pending', 'completed'];
  const tags = ['重要', '紧急', '新功能', '修复', '优化', '测试'];

  testData.value = Array.from({ length: size }, (_, i) => {
    const item: any = {
      id: i,
      name: `测试项目 ${i + 1}`,
      description: `这是第 ${i + 1} 个测试项目`,
      color: colors[i % colors.length],
      status: statuses[i % statuses.length],
      time: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString()
    };

    if (comp !== 'simple') {
      item.content =
        comp === 'complex'
          ? `这是一个复杂的项目描述，包含了大量的详细信息和说明。项目编号：${i + 1}，创建时间：${
              item.time
            }，包含多种功能特性和复杂的业务逻辑。`
          : `中等复杂度的项目描述 ${i + 1}`;
    }

    if (comp === 'complex') {
      item.tags = Array.from(
        { length: Math.floor(Math.random() * 4) + 1 },
        () => tags[Math.floor(Math.random() * tags.length)]
      );
    }

    return item;
  });

  addLog('success', `数据生成完成，共 ${testData.value.length} 条`);
};

// 性能测试
const runPerformanceTest = async () => {
  if (testing.value) return;

  testing.value = true;
  addLog('info', '开始性能测试');

  try {
    // 清空之前的结果
    Object.assign(virtualMetrics, { renderTime: 0, memoryUsage: 0, fps: 60, domNodes: 0 });
    Object.assign(normalMetrics, { renderTime: 0, memoryUsage: 0, fps: 60, domNodes: 0 });

    // 生成测试数据
    generateTestData();

    // 等待一帧
    await nextFrame();

    // 测试虚拟列表性能
    addLog('info', '测试虚拟列表性能');
    await testVirtualListPerformance();

    // 等待一帧
    await nextFrame();

    // 测试普通列表性能
    addLog('info', '测试普通列表性能');
    await testNormalListPerformance();

    addLog('success', '性能测试完成');
  } catch (error) {
    addLog('error', `测试失败: ${error}`);
  } finally {
    testing.value = false;
  }
};

const testVirtualListPerformance = async () => {
  const startTime = performance.now();

  // 重新渲染虚拟列表
  showVirtualList.value = false;
  await nextFrame();
  showVirtualList.value = true;
  await nextFrame();

  const renderTime = performance.now() - startTime;
  virtualMetrics.renderTime = Math.round(renderTime);

  // 模拟内存使用（实际项目中可以使用 performance.memory）
  virtualMetrics.memoryUsage = Math.round(dataSize.value * 0.1 + Math.random() * 5);

  // 模拟DOM节点数（虚拟列表只渲染可视区域）
  const visibleItems = Math.ceil(300 / itemHeight.value) + 10; // 缓冲区
  virtualMetrics.domNodes = Math.min(visibleItems, dataSize.value);

  // 模拟FPS（虚拟列表通常有更好的FPS）
  virtualMetrics.fps = Math.max(55, 60 - Math.floor(dataSize.value / 2000));

  addLog('success', `虚拟列表测试完成 - 渲染时间: ${virtualMetrics.renderTime}ms`);
};

const testNormalListPerformance = async () => {
  const startTime = performance.now();

  // 重新渲染普通列表
  showNormalList.value = false;
  await nextFrame();
  showNormalList.value = true;
  await nextFrame();

  const renderTime = performance.now() - startTime;
  normalMetrics.renderTime = Math.round(renderTime);

  // 模拟内存使用（普通列表渲染所有项目）
  const displayCount = Math.min(1000, dataSize.value);
  normalMetrics.memoryUsage = Math.round(displayCount * 0.5 + Math.random() * 10);

  // DOM节点数（普通列表渲染所有可见项目）
  normalMetrics.domNodes = displayCount;

  // 模拟FPS（普通列表性能随数据量下降）
  normalMetrics.fps = Math.max(30, 60 - Math.floor(displayCount / 20));

  addLog('success', `普通列表测试完成 - 渲染时间: ${normalMetrics.renderTime}ms`);
};

// 工具方法
const nextFrame = () => {
  return new Promise((resolve) => {
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(resolve);
    } else {
      setTimeout(resolve, 16);
    }
  });
};

const addLog = (type: 'info' | 'success' | 'error', message: string) => {
  testLog.value.push({
    type,
    message,
    time: new Date().toLocaleTimeString()
  });

  // 限制日志数量
  if (testLog.value.length > 50) {
    testLog.value.shift();
  }
};

const getBarWidth = (metric: string, type: 'virtual' | 'normal') => {
  const virtualValue = virtualMetrics[metric as keyof typeof virtualMetrics];
  const normalValue = normalMetrics[metric as keyof typeof normalMetrics];

  if (!virtualValue && !normalValue) return '0%';

  const maxValue = Math.max(virtualValue, normalValue);
  const value = type === 'virtual' ? virtualValue : normalValue;

  return `${(value / maxValue) * 100}%`;
};

const getImprovement = (metric: string) => {
  const virtualValue = virtualMetrics[metric as keyof typeof virtualMetrics];
  const normalValue = normalMetrics[metric as keyof typeof normalMetrics];

  if (!virtualValue || !normalValue) return '-';

  const improvement = ((normalValue - virtualValue) / normalValue) * 100;
  return improvement > 0 ? `↑${improvement.toFixed(1)}%` : `↓${Math.abs(improvement).toFixed(1)}%`;
};

// 事件处理
const onDataSizeChange = (e: any) => {
  dataSizeIndex.value = e.detail.value;
};

const onComplexityChange = (e: any) => {
  complexityIndex.value = e.detail.value;
};

const onVirtualScroll = (event: ScrollEvent) => {
  // 虚拟列表滚动处理
};

const onNormalScroll = (event: any) => {
  // 普通列表滚动处理
};

const clearResults = () => {
  Object.assign(virtualMetrics, { renderTime: 0, memoryUsage: 0, fps: 60, domNodes: 0 });
  Object.assign(normalMetrics, { renderTime: 0, memoryUsage: 0, fps: 60, domNodes: 0 });
  testLog.value = [];
  addLog('info', '结果已清空');
};

// 初始化
onMounted(() => {
  generateTestData();
});
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.demo-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.test-config {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-label {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.picker-value {
  padding: 5px 10px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  min-width: 60px;
  text-align: center;
}

.performance-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.metric-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
  text-align: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.metric-card {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #007aff;
  display: block;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666;
  display: block;
}

.performance-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
  text-align: center;
}

.chart-bars {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.chart-label {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.chart-bar-container {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.chart-bar {
  height: 100%;
  border-radius: 10px;
  position: absolute;
  top: 0;
  left: 0;
  transition: width 0.5s ease;
}

.chart-bar.virtual {
  background: linear-gradient(90deg, #4cd964, #5ac777);
  z-index: 2;
}

.chart-bar.normal {
  background: linear-gradient(90deg, #ff9500, #ffad33);
  z-index: 1;
}

.chart-improvement {
  font-size: 14px;
  font-weight: bold;
  color: #4cd964;
  min-width: 60px;
  text-align: right;
}

.test-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.test-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.test-btn.primary {
  background: #007aff;
  color: white;
}

.test-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.test-btn:disabled {
  opacity: 0.6;
}

.list-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.list-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 15px;
}

.list-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
  text-align: center;
}

.normal-list {
  border: 1px solid #eee;
  border-radius: 6px;
}

.test-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  background: white;
}

.test-item.simple {
  padding: 8px 10px;
}

.test-item.complex {
  padding: 15px 10px;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.item-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.item-info {
  flex: 1;
  margin-left: 10px;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
}

.item-desc {
  font-size: 12px;
  color: #666;
  display: block;
}

.item-index {
  font-size: 12px;
  color: #999;
}

.item-content {
  margin-bottom: 8px;
}

.content-text {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  display: block;
  margin-bottom: 5px;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 8px;
  font-size: 10px;
  color: #666;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.item-time {
  color: #999;
}

.item-status {
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;
}

.item-status.active {
  background: #e8f5e8;
  color: #4cd964;
}

.item-status.inactive {
  background: #ffe8e8;
  color: #ff3b30;
}

.item-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.item-status.completed {
  background: #e8f0ff;
  color: #007aff;
}

.test-log {
  background: white;
  border-radius: 8px;
  padding: 15px;
  max-height: 200px;
}

.log-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.log-content {
  height: 150px;
  font-family: monospace;
}

.log-item {
  font-size: 12px;
  line-height: 1.5;
  display: block;
  margin-bottom: 2px;
}

.log-item.info {
  color: #666;
}

.log-item.success {
  color: #4cd964;
}

.log-item.error {
  color: #ff3b30;
}
</style>
