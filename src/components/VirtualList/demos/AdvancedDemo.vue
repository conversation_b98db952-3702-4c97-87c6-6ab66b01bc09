<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">高级配置示例</text>
      <text class="demo-desc">展示虚拟列表的高级配置选项和自定义功能</text>
    </view>

    <!-- 配置面板 -->
    <view class="config-panel">
      <view class="config-section">
        <text class="section-title">基础配置</text>
        <view class="config-row">
          <text class="config-label">列表高度:</text>
          <slider
            :value="listHeight"
            :min="300"
            :max="800"
            class="config-slider"
            @change="onHeightChange"
          />
          <text class="config-value">{{ listHeight }}px</text>
        </view>
        <view class="config-row">
          <text class="config-label">项目高度:</text>
          <slider
            :value="itemHeight"
            :min="40"
            :max="120"
            class="config-slider"
            @change="onItemHeightChange"
          />
          <text class="config-value">{{ itemHeight }}px</text>
        </view>
      </view>

      <view class="config-section">
        <text class="section-title">性能配置</text>
        <view class="config-row">
          <text class="config-label">缓冲区大小:</text>
          <slider
            :value="bufferSize"
            :min="1"
            :max="20"
            class="config-slider"
            @change="onBufferSizeChange"
          />
          <text class="config-value">{{ bufferSize }}</text>
        </view>
        <view class="config-row">
          <text class="config-label">滚动阈值:</text>
          <slider
            :value="threshold"
            :min="0"
            :max="100"
            class="config-slider"
            @change="onThresholdChange"
          />
          <text class="config-value">{{ threshold }}px</text>
        </view>
      </view>

      <view class="config-section">
        <text class="section-title">功能开关</text>
        <view class="config-row">
          <text class="config-label">动态高度:</text>
          <switch :checked="dynamicHeight" @change="onDynamicHeightChange" />
        </view>
        <view class="config-row">
          <text class="config-label">增强模式:</text>
          <switch :checked="enhanced" @change="onEnhancedChange" />
        </view>
        <view class="config-row">
          <text class="config-label">回弹效果:</text>
          <switch :checked="bounces" @change="onBouncesChange" />
        </view>
        <view class="config-row">
          <text class="config-label">显示滚动条:</text>
          <switch :checked="showScrollbar" @change="onScrollbarChange" />
        </view>
      </view>
    </view>

    <!-- 实时统计 -->
    <view class="stats-panel">
      <view class="stat-card">
        <text class="stat-value">{{ visibleRange.start }}-{{ visibleRange.end }}</text>
        <text class="stat-label">可视范围</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ renderCount }}</text>
        <text class="stat-label">渲染数量</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ scrollSpeed }}px/s</text>
        <text class="stat-label">滚动速度</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{ Math.round(performanceStats.fps) }}</text>
        <text class="stat-label">FPS</text>
      </view>
    </view>

    <!-- 虚拟列表 -->
    <VirtualList
      :data="advancedData"
      :height="listHeight"
      :item-height="itemHeight"
      :dynamic-height="dynamicHeight"
      :buffer-size="bufferSize"
      :threshold="threshold"
      :enhanced="enhanced"
      :bounces="bounces"
      :show-scrollbar="showScrollbar"
      @scroll="onScroll"
      @item-visible="onItemVisible"
      @item-hidden="onItemHidden"
    >
      <template #default="{ item, index, isVisible }">
        <view
          class="advanced-item"
          :class="{
            visible: isVisible,
            dynamic: dynamicHeight,
            [`type-${item?.type || 'default'}`]: true
          }"
          :style="{ height: dynamicHeight ? 'auto' : `${itemHeight}px` }"
        >
          <view class="item-header">
            <view class="item-avatar" :style="{ background: item?.color || '#ccc' }">
              {{ item?.name?.charAt(0) || '?' }}
            </view>
            <view class="item-meta">
              <text class="item-name">{{ item?.name || '' }}</text>
              <text class="item-type">{{ getTypeLabel(item?.type || '') }}</text>
            </view>
            <view class="item-status" :class="item?.status || 'default'">
              {{ item?.status || '' }}
            </view>
          </view>

          <view v-if="item?.content" class="item-content">
            <text class="content-text">{{ item.content }}</text>
          </view>

          <view v-if="item?.tags && item.tags.length" class="item-tags">
            <text v-for="tag in item.tags" :key="tag" class="tag">
              {{ tag }}
            </text>
          </view>

          <view class="item-footer">
            <text class="item-time">{{ item?.time || '' }}</text>
            <text class="item-index">#{{ index + 1 }}</text>
            <text class="visibility-badge" :class="{ visible: isVisible }">
              {{ isVisible ? '👁️' : '🚫' }}
            </text>
          </view>
        </view>
      </template>
    </VirtualList>

    <!-- 操作按钮 -->
    <view class="demo-actions">
      <button class="action-btn primary" @click="generateData">重新生成数据</button>
      <button class="action-btn" @click="addComplexItems">添加复杂项</button>
      <button class="action-btn" @click="togglePerformanceMode">
        {{ performanceMode ? '关闭' : '开启' }}性能模式
      </button>
      <button class="action-btn secondary" @click="exportConfig">导出配置</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import VirtualList from '../index.vue';
import type { ScrollEvent } from '../types';
import { requestAnimationFrame } from '../utils/raf-polyfill';

// 配置参数
const listHeight = ref(500);
const itemHeight = ref(80);
const bufferSize = ref(5);
const threshold = ref(10);
const dynamicHeight = ref(false);
const enhanced = ref(false);
const bounces = ref(true);
const showScrollbar = ref(true);

// 数据和状态
const advancedData = ref<any[]>([]);
const visibleRange = reactive({ start: 0, end: 0 });
const renderCount = ref(0);
const scrollSpeed = ref(0);
const performanceMode = ref(false);

// 性能监控
const performanceStats = reactive({
  fps: 60,
  lastTime: 0,
  frameCount: 0
});

// 性能监控控制
let performanceMonitorId: any = null;

// 滚动相关
let lastScrollTop = 0;
let lastScrollTime = 0;

// 数据类型配置
const itemTypes = [
  { type: 'user', label: '用户', color: '#007aff' },
  { type: 'product', label: '产品', color: '#4cd964' },
  { type: 'order', label: '订单', color: '#ff9500' },
  { type: 'message', label: '消息', color: '#ff3b30' },
  { type: 'task', label: '任务', color: '#5856d6' }
];

const statusOptions = ['active', 'inactive', 'pending', 'completed'];
const tagOptions = ['重要', '紧急', '新功能', '修复', '优化', '测试', '文档', '设计'];

// 工具方法
const updateRenderCount = () => {
  renderCount.value = Math.max(0, visibleRange.end - visibleRange.start + 1);
};

const getTypeLabel = (type: string) => {
  const typeConfig = itemTypes.find((t) => t.type === type);
  return typeConfig?.label || type;
};

// 生成测试数据
const generateData = () => {
  const count = performanceMode.value ? 10000 : 2000;
  advancedData.value = Array.from({ length: count }, (_, i) => {
    const typeConfig = itemTypes[i % itemTypes.length];
    const hasContent = Math.random() > 0.3;
    const hasLongContent = Math.random() > 0.7;
    const tagCount = Math.floor(Math.random() * 4);

    return {
      id: i,
      name: `${typeConfig.label} ${i + 1}`,
      type: typeConfig.type,
      color: typeConfig.color,
      status: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      content: hasContent
        ? hasLongContent
          ? `这是一个很长的内容描述，包含了详细的信息和说明。项目编号：${
            i + 1
          }，创建时间：${new Date().toLocaleString()}，状态更新记录等等。`
          : `简短的描述内容 ${i + 1}`
        : null,
      tags: Array.from(
        { length: tagCount },
        () => tagOptions[Math.floor(Math.random() * tagOptions.length)]
      ),
      time: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString(),
      complexity: hasLongContent ? 'high' : hasContent ? 'medium' : 'low'
    };
  });
};

// 初始化
onMounted(() => {
  generateData();
  startPerformanceMonitoring();
});

// 清理资源
onUnmounted(() => {
  stopPerformanceMonitoring();
});

// 配置变更处理
const onHeightChange = (e: any) => {
  listHeight.value = e.detail.value;
};

const onItemHeightChange = (e: any) => {
  itemHeight.value = e.detail.value;
};

const onBufferSizeChange = (e: any) => {
  bufferSize.value = e.detail.value;
};

const onThresholdChange = (e: any) => {
  threshold.value = e.detail.value;
};

const onDynamicHeightChange = (e: any) => {
  dynamicHeight.value = e.detail.value;
};

const onEnhancedChange = (e: any) => {
  enhanced.value = e.detail.value;
};

const onBouncesChange = (e: any) => {
  bounces.value = e.detail.value;
};

const onScrollbarChange = (e: any) => {
  showScrollbar.value = e.detail.value;
};

// 事件处理
const onScroll = (event: ScrollEvent) => {
  const currentTime = Date.now();
  const currentScrollTop = event.detail.scrollTop;

  if (lastScrollTime > 0) {
    const timeDiff = currentTime - lastScrollTime;
    const scrollDiff = Math.abs(currentScrollTop - lastScrollTop);
    scrollSpeed.value = Math.round((scrollDiff / timeDiff) * 1000);
  }

  lastScrollTop = currentScrollTop;
  lastScrollTime = currentTime;
};

const onItemVisible = (index: number, _item: any) => {
  if (index < visibleRange.start || visibleRange.start === 0) {
    visibleRange.start = index;
  }
  if (index > visibleRange.end) {
    visibleRange.end = index;
  }
  updateRenderCount();
};

const onItemHidden = (_index: number, _item: any) => {
  updateRenderCount();
};

const addComplexItems = () => {
  const complexItems = Array.from({ length: 50 }, (_, i) => {
    const index = advancedData.value.length + i;
    return {
      id: index,
      name: `复杂项目 ${index + 1}`,
      type: 'complex',
      color: '#8e44ad',
      status: 'active',
      content: `这是一个包含大量内容的复杂项目。包含多行文本、丰富的标签信息，以及各种复杂的数据结构。项目创建于 ${new Date().toLocaleString()}，具有高度的复杂性和丰富的功能特性。`,
      tags: ['复杂', '多功能', '高级', '定制'],
      time: new Date().toLocaleString(),
      complexity: 'very-high'
    };
  });

  advancedData.value.push(...complexItems);
  uni.showToast({
    title: `已添加 ${complexItems.length} 个复杂项`,
    icon: 'none'
  });
};

const togglePerformanceMode = () => {
  performanceMode.value = !performanceMode.value;
  generateData();
  uni.showToast({
    title: `${performanceMode.value ? '已开启' : '已关闭'}性能模式`,
    icon: 'none'
  });
};

const exportConfig = () => {
  const config = {
    listHeight: listHeight.value,
    itemHeight: itemHeight.value,
    bufferSize: bufferSize.value,
    threshold: threshold.value,
    dynamicHeight: dynamicHeight.value,
    enhanced: enhanced.value,
    bounces: bounces.value,
    showScrollbar: showScrollbar.value,
    performanceMode: performanceMode.value
  };

  console.log('当前配置:', config);
  uni.showModal({
    title: '配置导出',
    content: JSON.stringify(config, null, 2),
    showCancel: false
  });
};

// 性能监控
const startPerformanceMonitoring = () => {
  const monitor = () => {
    const now = Date.now();
    performanceStats.frameCount++;

    if (now - performanceStats.lastTime >= 1000) {
      performanceStats.fps = Math.round(
        (performanceStats.frameCount * 1000) / (now - performanceStats.lastTime)
      );
      performanceStats.frameCount = 0;
      performanceStats.lastTime = now;
    }

    performanceMonitorId = requestAnimationFrame(monitor);
  };

  performanceStats.lastTime = Date.now();
  performanceMonitorId = requestAnimationFrame(monitor);
};

// 停止性能监控
const stopPerformanceMonitoring = () => {
  if (performanceMonitorId) {
    // 使用clearTimeout因为我们的polyfill返回的是setTimeout的ID
    clearTimeout(performanceMonitorId);
    performanceMonitorId = null;
  }
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.demo-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.config-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-section {
  margin-bottom: 20px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.config-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;
}

.config-label {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.config-slider {
  flex: 1;
}

.config-value {
  font-size: 14px;
  color: #007aff;
  font-weight: bold;
  min-width: 60px;
  text-align: right;
}

.stats-panel {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #007aff;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  display: block;
}

.advanced-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  opacity: 0.7;
}

.advanced-item.visible {
  opacity: 1;
  transform: translateX(0);
}

.advanced-item.dynamic {
  min-height: auto;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.item-meta {
  flex: 1;
  margin-left: 12px;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 2px;
}

.item-type {
  font-size: 12px;
  color: #666;
  display: block;
}

.item-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.item-status.active {
  background: #e8f5e8;
  color: #4cd964;
}

.item-status.inactive {
  background: #ffe8e8;
  color: #ff3b30;
}

.item-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.item-status.completed {
  background: #e8f0ff;
  color: #007aff;
}

.item-content {
  margin-bottom: 10px;
}

.content-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 10px;
}

.tag {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 10px;
  font-size: 11px;
  color: #666;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.visibility-badge {
  transition: all 0.3s;
}

.visibility-badge.visible {
  transform: scale(1.2);
}

.demo-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.action-btn {
  flex: 1;
  min-width: 120px;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.action-btn:not(.primary):not(.secondary) {
  background: #4cd964;
  color: white;
}

.action-btn:active {
  opacity: 0.8;
}
</style>
