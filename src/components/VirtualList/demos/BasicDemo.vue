<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">基础虚拟列表示例</text>
      <text class="demo-desc">展示基本的虚拟列表功能，包含1000个简单列表项</text>
    </view>

    <view class="demo-stats">
      <text class="stat-item">总数据: {{ basicData.length }}</text>
      <text class="stat-item">当前滚动位置: {{ scrollPosition }}px</text>
    </view>

    <VirtualList
      ref="virtualListRef"
      :data="basicData"
      :height="400"
      :item-height="60"
      :buffer-size="5"
      @scroll="onScroll"
      @scroll-to-top="onScrollToTop"
      @scroll-to-bottom="onScrollToBottom"
    >
      <template #default="{ item, index }">
        <view v-if="item" class="basic-item" :class="{ even: index % 2 === 0 }">
          <view class="item-index">{{ index + 1 }}</view>
          <view class="item-content">
            <text class="item-title">{{ item.title || '' }}</text>
            <text class="item-subtitle">{{ item.subtitle || '' }}</text>
          </view>
          <view class="item-time">{{ item.time || '' }}</view>
        </view>
      </template>
    </VirtualList>

    <view class="demo-actions">
      <button class="action-btn" @click="scrollToTop">回到顶部</button>
      <button class="action-btn" @click="scrollToBottom">滚动到底部</button>
      <button class="action-btn" @click="addItems">添加数据</button>
      <button class="action-btn" @click="clearItems">清空数据</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '../index.vue';
import type { ScrollEvent } from '../types';

// 组件名称（替代defineOptions）
const __name = 'BasicDemo';

// 基础数据
const basicData = ref<any[]>([]);
const scrollPosition = ref(0);

// VirtualList组件引用
const virtualListRef = ref<any>(null);

// 生成测试数据
const generateData = (count: number, startIndex = 0) => {
  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i;
    return {
      id: index,
      title: `列表项 ${index + 1}`,
      subtitle: `这是第 ${index + 1} 个列表项的副标题`,
      time: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleDateString()
    };
  });
};

// 初始化数据
onMounted(() => {
  basicData.value = generateData(1000);
});

// 事件处理
const onScroll = (event: ScrollEvent) => {
  scrollPosition.value = Math.round(event.detail.scrollTop);
};

const onScrollToTop = () => {
  uni.showToast({
    title: '已到达顶部',
    icon: 'none'
  });
};

const onScrollToBottom = () => {
  uni.showToast({
    title: '已到达底部',
    icon: 'none'
  });
};

// 操作方法
const scrollToTop = () => {
  if (virtualListRef.value && virtualListRef.value.scrollToTop) {
    virtualListRef.value.scrollToTop();
    uni.showToast({
      title: '已滚动到顶部',
      icon: 'none'
    });
  } else {
    uni.showToast({
      title: '滚动功能暂不可用',
      icon: 'none'
    });
  }
};

const scrollToBottom = () => {
  if (virtualListRef.value && virtualListRef.value.scrollToBottom) {
    virtualListRef.value.scrollToBottom();
    uni.showToast({
      title: '已滚动到底部',
      icon: 'none'
    });
  } else {
    uni.showToast({
      title: '滚动功能暂不可用',
      icon: 'none'
    });
  }
};

const addItems = () => {
  const newItems = generateData(100, basicData.value.length);
  basicData.value.push(...newItems);
  uni.showToast({
    title: `已添加 ${newItems.length} 项`,
    icon: 'none'
  });
};

const clearItems = () => {
  basicData.value = [];
  uni.showToast({
    title: '已清空数据',
    icon: 'none'
  });
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.demo-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.demo-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 10px;
  background: white;
  border-radius: 8px;
}

.stat-item {
  font-size: 14px;
  color: #333;
}

.basic-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.basic-item.even {
  background: #fafafa;
}

.basic-item:active {
  background: #e6f3ff;
}

.item-index {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.item-content {
  flex: 1;
  margin-left: 15px;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.item-subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.item-time {
  font-size: 12px;
  color: #999;
}

.demo-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
}

.action-btn {
  flex: 1;
  min-width: 80px;
  padding: 10px 15px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.action-btn:active {
  background: #0056cc;
}
</style>
