<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">图片列表示例</text>
      <text class="demo-desc">模拟游戏地图场景，展示图片懒加载和性能优化</text>
    </view>

    <view class="demo-controls">
      <view class="control-group">
        <text class="control-label">缓冲区大小:</text>
        <picker :range="bufferOptions" @change="onBufferChange">
          <view class="picker-value">{{ bufferSize }}</view>
        </picker>
      </view>
      <view class="control-group">
        <text class="control-label">懒加载:</text>
        <switch :checked="lazyLoad" @change="onLazyLoadChange" />
      </view>
    </view>

    <view class="demo-stats">
      <text class="stat-item">地图数量: {{ imageData.length }}</text>
      <text class="stat-item">可见项: {{ visibleCount }}</text>
      <text class="stat-item">加载图片: {{ loadedImages }}</text>
    </view>

    <VirtualList
      :data="imageData"
      :height="500"
      :item-height="220"
      :buffer-size="bufferSize"
      @scroll="onScroll"
      @item-visible="onItemVisible"
      @item-hidden="onItemHidden"
    >
      <template #default="{ item, index, isVisible }">
        <view class="image-item">
          <view class="image-container">
            <image
              v-if="!lazyLoad || isVisible"
              :src="item.imageUrl"
              class="map-image"
              mode="aspectFill"
              :lazy-load="lazyLoad"
              @load="onImageLoad"
              @error="onImageError"
            />
            <view v-else class="image-placeholder">
              <text class="placeholder-text">{{ item?.name || '' }}</text>
            </view>

            <view class="image-overlay">
              <view class="map-info">
                <text class="map-name">{{ item?.name || '' }}</text>
                <text class="map-level">等级: {{ item?.level || '' }}</text>
                <text class="map-difficulty">难度: {{ item?.difficulty || '' }}</text>
              </view>
              <view class="map-status" :class="item.status">
                {{ getStatusText(item.status) }}
              </view>
            </view>

            <view class="visibility-indicator" :class="{ visible: isVisible }">
              {{ isVisible ? '可见' : '隐藏' }}
            </view>
          </view>

          <view class="item-actions">
            <button class="action-btn primary" @click="enterMap(item)">进入地图</button>
            <button class="action-btn secondary" @click="viewDetails(item)">查看详情</button>
          </view>
        </view>
      </template>

      <template #empty>
        <view class="empty-state">
          <text class="empty-text">暂无地图数据</text>
          <button class="load-btn" @click="loadMaps">加载地图</button>
        </view>
      </template>
    </VirtualList>

    <view class="demo-actions">
      <button class="action-btn" @click="addRandomMaps">添加随机地图</button>
      <button class="action-btn" @click="clearMaps">清空地图</button>
      <button class="action-btn" @click="resetStats">重置统计</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '../index.vue';
import type { ScrollEvent } from '../types';

// 控制参数
const bufferSize = ref(3);
const bufferOptions = [1, 3, 5, 8, 10];
const lazyLoad = ref(true);

// 数据和统计
const imageData = ref<any[]>([]);
const visibleCount = ref(0);
const loadedImages = ref(0);
const visibleItems = new Set<number>();

// 地图数据模板
const mapTemplates = [
  { name: '新手村', level: '1-10', difficulty: '简单', status: 'unlocked' },
  { name: '森林迷宫', level: '10-20', difficulty: '普通', status: 'unlocked' },
  { name: '火山洞穴', level: '20-30', difficulty: '困难', status: 'locked' },
  { name: '冰雪王国', level: '30-40', difficulty: '困难', status: 'unlocked' },
  { name: '沙漠绿洲', level: '40-50', difficulty: '普通', status: 'unlocked' },
  { name: '深海宫殿', level: '50-60', difficulty: '极难', status: 'locked' },
  { name: '天空之城', level: '60-70', difficulty: '极难', status: 'locked' },
  { name: '暗影之地', level: '70-80', difficulty: '传说', status: 'locked' },
  { name: '光明圣殿', level: '80-90', difficulty: '传说', status: 'locked' },
  { name: '终极试炼', level: '90-100', difficulty: '史诗', status: 'locked' }
];

// 生成地图数据
const generateMapData = (count: number) => {
  return Array.from({ length: count }, (_, i) => {
    const template = mapTemplates[i % mapTemplates.length];
    return {
      id: i,
      name: `${template.name} ${Math.floor(i / mapTemplates.length) + 1}`,
      level: template.level,
      difficulty: template.difficulty,
      status: template.status,
      imageUrl: `https://picsum.photos/300/160?random=${i}`, // 使用随机图片服务
      description: `这是${template.name}的详细描述，包含了丰富的游戏内容和挑战。`
    };
  });
};

// 初始化
onMounted(() => {
  imageData.value = generateMapData(50);
});

// 事件处理
const onScroll = (event: ScrollEvent) => {
  // 滚动时的处理逻辑
};

const onItemVisible = (index: number, item: any) => {
  visibleItems.add(index);
  visibleCount.value = visibleItems.size;
  console.log(`地图 ${item.name} 进入可视区域`);
};

const onItemHidden = (index: number, item: any) => {
  visibleItems.delete(index);
  visibleCount.value = visibleItems.size;
  console.log(`地图 ${item.name} 离开可视区域`);
};

const onImageLoad = () => {
  loadedImages.value++;
};

const onImageError = () => {
  console.warn('图片加载失败');
};

// 控制事件
const onBufferChange = (e: any) => {
  bufferSize.value = bufferOptions[e.detail.value];
};

const onLazyLoadChange = (e: any) => {
  lazyLoad.value = e.detail.value;
};

// 操作方法
const enterMap = (item: any) => {
  if (item.status === 'locked') {
    uni.showToast({
      title: '地图未解锁',
      icon: 'none'
    });
    return;
  }

  uni.showModal({
    title: '进入地图',
    content: `确定要进入 ${item.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: `正在进入 ${item.name}`,
          icon: 'loading'
        });
      }
    }
  });
};

const viewDetails = (item: any) => {
  uni.showModal({
    title: item.name,
    content: `等级: ${item.level}\n难度: ${item.difficulty}\n状态: ${getStatusText(
      item.status
    )}\n\n${item.description}`,
    showCancel: false
  });
};

const addRandomMaps = () => {
  const newMaps = generateMapData(20);
  const startIndex = imageData.value.length;
  newMaps.forEach((map, i) => {
    map.id = startIndex + i;
  });
  imageData.value.push(...newMaps);

  uni.showToast({
    title: `已添加 ${newMaps.length} 个地图`,
    icon: 'none'
  });
};

const clearMaps = () => {
  imageData.value = [];
  resetStats();
  uni.showToast({
    title: '已清空所有地图',
    icon: 'none'
  });
};

const loadMaps = () => {
  imageData.value = generateMapData(30);
  uni.showToast({
    title: '地图加载完成',
    icon: 'success'
  });
};

const resetStats = () => {
  visibleItems.clear();
  visibleCount.value = 0;
  loadedImages.value = 0;
};

// 工具方法
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unlocked: '已解锁',
    locked: '未解锁',
    completed: '已完成'
  };
  return statusMap[status] || status;
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 20px;
}

.demo-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.demo-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.demo-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 15px;
  background: white;
  border-radius: 8px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-label {
  font-size: 14px;
  color: #333;
}

.picker-value {
  padding: 5px 10px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

.demo-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 10px 15px;
  background: white;
  border-radius: 8px;
}

.stat-item {
  font-size: 14px;
  color: #333;
}

.image-item {
  margin-bottom: 15px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  height: 180px;
}

.map-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 15px 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.map-info {
  flex: 1;
}

.map-name {
  color: white;
  font-size: 18px;
  font-weight: bold;
  display: block;
  margin-bottom: 4px;
}

.map-level,
.map-difficulty {
  color: #ccc;
  font-size: 14px;
  display: block;
  margin-bottom: 2px;
}

.map-status {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.map-status.unlocked {
  background: #4cd964;
  color: white;
}

.map-status.locked {
  background: #ff3b30;
  color: white;
}

.map-status.completed {
  background: #ff9500;
  color: white;
}

.visibility-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  transition: all 0.3s;
}

.visibility-indicator.visible {
  background: rgba(76, 217, 100, 0.9);
}

.item-actions {
  display: flex;
  gap: 10px;
  padding: 15px;
}

.action-btn {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.action-btn:active {
  opacity: 0.8;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 20px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

.load-btn {
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
}

.demo-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
}

.demo-actions .action-btn {
  flex: 1;
  background: #007aff;
  color: white;
}
</style>
