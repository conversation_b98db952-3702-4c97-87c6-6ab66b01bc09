<template>
  <view class="test-container">
    <text class="test-title">VirtualList 简单测试</text>

    <VirtualList :data="testData" :height="300" :item-height="50">
      <template #default="{ item, index }">
        <view v-if="item" class="test-item">
          <text>{{ index + 1 }}. {{ item?.title || '无标题' }}</text>
        </view>
      </template>
    </VirtualList>

    <button class="test-btn" @click="addData">添加数据</button>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import VirtualList from '../index.vue';

const testData = ref<any[]>([]);

const addData = () => {
  testData.value = Array.from({ length: 100 }, (_, i) => ({
    id: i,
    title: `测试项目 ${i + 1}`
  }));
};

onMounted(() => {
  addData();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  display: block;
}

.test-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.test-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
}
</style>
