/**
 * 滚动管理Hook
 * Scroll Manager Hook
 */

import { onUnmounted, ref } from 'vue';
import { ScrollManager } from '../utils/scroll-manager';
import type { ScrollEvent } from '../types';

export interface UseScrollManagerOptions {
  // 滚动事件节流间隔（毫秒）
  throttleDelay?: number;
  // 滚动结束检测延迟（毫秒）
  scrollEndDelay?: number;
  // 事件回调
  onScroll?: (event: ScrollEvent) => void;
  onScrollToTop?: () => void;
  onScrollToBottom?: () => void;
  onScrollStart?: () => void;
  onScrollEnd?: () => void;
}

export function useScrollManager(options: UseScrollManagerOptions = {}) {
  // 根据平台调整节流延迟
  const isMP = typeof uni !== 'undefined' && uni.getSystemInfoSync().platform !== 'h5';
  const defaultThrottleDelay = isMP ? 50 : 16; // 小程序端使用更大的节流延迟

  const {
    throttleDelay = defaultThrottleDelay,
    scrollEndDelay = 150,
    onScroll,
    onScrollToTop,
    onScrollToBottom,
    onScrollStart,
    onScrollEnd
  } = options;

  // 滚动管理器实例
  const scrollManager = new ScrollManager();

  // 响应式状态
  const scrollTop = ref(0);
  const isScrolling = ref(false);
  const scrollProgress = ref(0);
  const canScrollUp = ref(false);
  const canScrollDown = ref(false);

  // 节流相关
  let throttleTimer: number | null = null;
  let scrollEndTimer: number | null = null;
  let lastScrollTime = 0;

  // 更新滚动状态
  const updateScrollState = () => {
    scrollTop.value = scrollManager.getScrollTop();
    isScrolling.value = scrollManager.getIsScrolling();
    scrollProgress.value = scrollManager.getScrollProgress();
    canScrollUp.value = scrollManager.canScrollUp();
    canScrollDown.value = scrollManager.canScrollDown();
  };

  // 节流处理滚动事件
  const throttledHandleScroll = (event: ScrollEvent) => {
    const now = Date.now();

    // 如果不在滚动状态，触发滚动开始事件
    if (!isScrolling.value && onScrollStart) {
      onScrollStart();
    }

    // 清除现有的节流定时器
    if (throttleTimer) {
      clearTimeout(throttleTimer);
    }

    // 清除滚动结束定时器
    if (scrollEndTimer) {
      clearTimeout(scrollEndTimer);
    }

    // 立即更新滚动位置（避免滚动延迟）
    scrollManager.handleScroll(event);
    updateScrollState();

    // 节流处理其他逻辑
    if (now - lastScrollTime >= throttleDelay) {
      lastScrollTime = now;

      // 触发滚动事件回调
      if (onScroll) {
        onScroll(event);
      }
    } else {
      // 延迟执行
      throttleTimer = setTimeout(() => {
        if (onScroll) {
          onScroll(event);
        }
        lastScrollTime = Date.now();
      }, throttleDelay) as unknown as number;
    }

    // 设置滚动结束检测
    scrollEndTimer = setTimeout(() => {
      if (isScrolling.value && onScrollEnd) {
        onScrollEnd();
      }
      updateScrollState();
    }, scrollEndDelay) as unknown as number;
  };

  // 处理滚动事件
  const handleScroll = (event: ScrollEvent) => {
    throttledHandleScroll(event);
  };

  // 设置滚动回调（用于程序化滚动）
  const setScrollToCallback = (callback: (scrollTop: number) => void) => {
    scrollManager.setScrollToCallback(callback);
  };

  // 设置事件回调
  const setupEventCallbacks = () => {
    scrollManager.setEventCallbacks({
      onScroll: (event: ScrollEvent) => {
        updateScrollState();
        if (onScroll) {
          onScroll(event);
        }
      },
      onScrollToTop: () => {
        updateScrollState();
        if (onScrollToTop) {
          onScrollToTop();
        }
      },
      onScrollToBottom: () => {
        updateScrollState();
        if (onScrollToBottom) {
          onScrollToBottom();
        }
      }
    });
  };

  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    scrollManager.scrollToIndex(index);
    updateScrollState();
  };

  // 滚动到顶部
  const scrollToTop = () => {
    scrollManager.scrollToTop();
    updateScrollState();
  };

  // 滚动到底部
  const scrollToBottom = () => {
    scrollManager.scrollToBottom();
    updateScrollState();
  };

  // 平滑滚动到指定位置
  const smoothScrollTo = (targetScrollTop: number, duration?: number) => {
    scrollManager.smoothScrollTo(targetScrollTop, duration);
    updateScrollState();
  };

  // 设置滚动位置（不触发动画）
  const setScrollTop = (scrollTop: number) => {
    scrollManager.setScrollTop(scrollTop);
    updateScrollState();
  };

  // 获取滚动管理器实例
  const getScrollManager = () => scrollManager;

  // 初始化
  const initialize = () => {
    setupEventCallbacks();
    updateScrollState();
  };

  // 清理定时器
  const clearTimers = () => {
    if (throttleTimer) {
      clearTimeout(throttleTimer);
      throttleTimer = null;
    }
    if (scrollEndTimer) {
      clearTimeout(scrollEndTimer);
      scrollEndTimer = null;
    }
  };

  // 清理资源
  const destroy = () => {
    clearTimers();
    scrollManager.destroy();
  };

  // 组件卸载时清理
  onUnmounted(() => {
    destroy();
  });

  // 初始化
  initialize();

  return {
    // 响应式状态
    scrollTop,
    isScrolling,
    scrollProgress,
    canScrollUp,
    canScrollDown,

    // 滚动方法
    handleScroll,
    scrollToIndex,
    scrollToTop,
    scrollToBottom,
    smoothScrollTo,
    setScrollTop,

    // 配置方法
    setScrollToCallback,
    getScrollManager,

    // 生命周期方法
    initialize,
    destroy
  };
}
