/**
 * 平台优化Hook
 * Platform Optimization Hook
 */

import { onMounted, onUnmounted, ref } from 'vue';
import {
    type PlatformSpecificConfig,
    type PlatformType,
    getCurrentPlatform,
    getCurrentPlatformConfig,
    platformScrollImplementation
} from '../utils/platform-adapter';

export interface UsePlatformOptimizeOptions {
    // 是否启用图片懒加载
    enableImageLazyLoad?: boolean;
    // 是否启用性能监控
    enablePerformanceMonitoring?: boolean;
    // 自定义平台配置
    customConfig?: Partial<PlatformSpecificConfig>;
    // 性能监控回调
    onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
}

export interface PerformanceMetrics {
    // 渲染性能
    renderTime: number;
    // 滚动性能
    scrollFPS: number;
    // 内存使用
    memoryUsage: number;
    // 可见元素数量
    visibleItemCount: number;
    // 总元素数量
    totalItemCount: number;
    // 平台信息
    platform: PlatformType;
}

export interface ImageLazyLoadManager {
    // 观察元素
    observe: (element: Element) => void;
    // 取消观察
    unobserve: (element: Element) => void;
    // 销毁
    destroy: () => void;
}

export function usePlatformOptimize(options: UsePlatformOptimizeOptions = {}) {
    const {
        enableImageLazyLoad = true,
        enablePerformanceMonitoring = false,
        customConfig = {},
        onPerformanceUpdate
    } = options;

    // 平台信息
    const platform = ref<PlatformType>(getCurrentPlatform());
    const platformConfig = ref<PlatformSpecificConfig>({
        ...getCurrentPlatformConfig(),
        ...customConfig
    });

    // 性能监控相关
    const performanceMetrics = ref<PerformanceMetrics>({
        renderTime: 0,
        scrollFPS: 60,
        memoryUsage: 0,
        visibleItemCount: 0,
        totalItemCount: 0,
        platform: platform.value
    });

    // 图片懒加载管理器
    const imageLazyLoadManager = ref<ImageLazyLoadManager | null>(null);

    // 性能监控定时器
    let performanceTimer: number | null = null;
    let fpsCounter = 0;
    let lastFpsTime = 0;

    // 创建图片懒加载管理器
    const createImageLazyLoadManager = (): ImageLazyLoadManager | null => {
        if (!enableImageLazyLoad) return null;

        const observer = platformScrollImplementation.createIntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const img = entry.target as HTMLImageElement;
                    const dataSrc = img.getAttribute('data-src');
                    if (dataSrc) {
                        img.src = dataSrc;
                        img.removeAttribute('data-src');
                        if (observer) {
                            observer.unobserve(img);
                        }
                    }
                }
            });
        });

        if (!observer) return null;

        return {
            observe: (element: Element) => {
                if (observer && observer.observe) {
                    observer.observe(element);
                }
            },
            unobserve: (element: Element) => {
                if (observer && observer.unobserve) {
                    observer.unobserve(element);
                }
            },
            destroy: () => {
                if (observer && observer.disconnect) {
                    observer.disconnect();
                }
            }
        };
    };

    // 初始化图片懒加载
    const initializeImageLazyLoad = () => {
        if (enableImageLazyLoad) {
            imageLazyLoadManager.value = createImageLazyLoadManager();
        }
    };

    // 优化图片加载
    const optimizeImageLoading = (container: Element) => {
        if (!imageLazyLoadManager.value) return;

        const images = container.querySelectorAll('img[data-src]');
        images.forEach((img) => {
            imageLazyLoadManager.value!.observe(img);
        });
    };

    // 开始性能监控
    const startPerformanceMonitoring = () => {
        if (!enablePerformanceMonitoring) return;

        lastFpsTime = performance.now();

        const updatePerformanceMetrics = () => {
            const now = performance.now();
            fpsCounter++;

            // 每秒更新一次FPS
            if (now - lastFpsTime >= 1000) {
                performanceMetrics.value.scrollFPS = Math.round((fpsCounter * 1000) / (now - lastFpsTime));
                fpsCounter = 0;
                lastFpsTime = now;

                // 获取内存使用情况（仅在支持的环境中）
                if ('memory' in performance) {
                    const memory = (performance as any).memory;
                    performanceMetrics.value.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
                }

                // 触发性能更新回调
                if (onPerformanceUpdate) {
                    onPerformanceUpdate(performanceMetrics.value);
                }
            }

            performanceTimer = requestAnimationFrame(updatePerformanceMetrics) as unknown as number;
        };

        performanceTimer = requestAnimationFrame(updatePerformanceMetrics) as unknown as number;
    };

    // 停止性能监控
    const stopPerformanceMonitoring = () => {
        if (performanceTimer) {
            cancelAnimationFrame(performanceTimer);
            performanceTimer = null;
        }
    };

    // 更新性能指标
    const updatePerformanceMetrics = (metrics: Partial<PerformanceMetrics>) => {
        Object.assign(performanceMetrics.value, metrics);
    };

    // 获取滚动容器属性
    const getScrollViewProps = () => {
        return platformScrollImplementation.getScrollViewProps();
    };

    // 获取滚动事件配置
    const getScrollEventConfig = () => {
        return platformScrollImplementation.getScrollEventConfig();
    };

    // 获取平台样式
    const getPlatformStyles = () => {
        return platformScrollImplementation.getPlatformStyles();
    };

    // 应用滚动优化
    const applyScrollOptimizations = (element: Element) => {
        const styles = getPlatformStyles();
        const htmlElement = element as HTMLElement;

        Object.entries(styles).forEach(([property, value]) => {
            htmlElement.style.setProperty(property, value);
        });

        // 平台特定优化
        switch (platform.value) {
            case 'mp-weixin':
                // 小程序特定优化
                htmlElement.style.setProperty('-webkit-overflow-scrolling', 'touch');
                break;

            case 'app':
                // App端特定优化
                htmlElement.style.setProperty('overflow-scrolling', 'touch');
                break;

            case 'h5':
                // H5特定优化
                if (!platformConfig.value.showScrollbar) {
                    htmlElement.style.setProperty('scrollbar-width', 'none');
                    htmlElement.style.setProperty('-ms-overflow-style', 'none');
                    htmlElement.style.setProperty('::-webkit-scrollbar', 'display: none');
                }
                break;
        }
    };

    // 应用内存优化
    const applyMemoryOptimizations = () => {
        // 根据平台调整缓冲区大小
        const bufferMultiplier = platformConfig.value.bufferSizeMultiplier;
        return Math.ceil(5 * bufferMultiplier); // 基础缓冲区大小为5
    };

    // 应用渲染优化
    const applyRenderOptimizations = () => {
        const optimizations: Record<string, any> = {};

        // 启用硬件加速
        if (platformConfig.value.enableHardwareAcceleration) {
            optimizations.useGPU = true;
            optimizations.willChange = 'transform';
        }

        // 使用transform优化
        if (platformConfig.value.useTransform) {
            optimizations.useTransform = true;
            optimizations.backfaceVisibility = 'hidden';
        }

        return optimizations;
    };

    // 检查平台能力
    const checkPlatformCapabilities = () => {
        return {
            supportsIntersectionObserver: platformConfig.value.useIntersectionObserver,
            supportsPassiveEvents: platformConfig.value.enablePassiveScroll,
            supportsHardwareAcceleration: platformConfig.value.enableHardwareAcceleration,
            supportsImageLazyLoad: enableImageLazyLoad && !!imageLazyLoadManager.value
        };
    };

    // 获取优化建议
    const getOptimizationRecommendations = () => {
        const recommendations: string[] = [];

        if (platform.value === 'mp-weixin') {
            recommendations.push('建议启用enhanced模式以获得更好的滚动性能');
            recommendations.push('使用scroll-view的enable-passive属性优化滚动事件');
        }

        if (platform.value === 'app') {
            recommendations.push('建议增加缓冲区大小以减少滚动时的白屏');
            recommendations.push('启用硬件加速以提升渲染性能');
        }

        if (platform.value === 'h5') {
            recommendations.push('建议使用Intersection Observer API进行图片懒加载');
            recommendations.push('启用被动滚动事件监听以提升性能');
        }

        return recommendations;
    };

    // 初始化优化
    const initializeOptimizations = () => {
        initializeImageLazyLoad();
        if (enablePerformanceMonitoring) {
            startPerformanceMonitoring();
        }
    };

    // 清理优化
    const cleanupOptimizations = () => {
        if (imageLazyLoadManager.value) {
            imageLazyLoadManager.value.destroy();
            imageLazyLoadManager.value = null;
        }
        stopPerformanceMonitoring();
    };

    // 组件挂载时初始化
    onMounted(() => {
        initializeOptimizations();
    });

    // 组件卸载时清理
    onUnmounted(() => {
        cleanupOptimizations();
    });

    return {
        // 平台信息
        platform,
        platformConfig,
        performanceMetrics,

        // 图片懒加载
        imageLazyLoadManager,
        optimizeImageLoading,

        // 性能监控
        startPerformanceMonitoring,
        stopPerformanceMonitoring,
        updatePerformanceMetrics,

        // 平台配置
        getScrollViewProps,
        getScrollEventConfig,
        getPlatformStyles,

        // 优化应用
        applyScrollOptimizations,
        applyMemoryOptimizations,
        applyRenderOptimizations,

        // 平台能力检查
        checkPlatformCapabilities,
        getOptimizationRecommendations,

        // 生命周期
        initializeOptimizations,
        cleanupOptimizations
    };
}
