/**
 * 虚拟列表主要逻辑Hook
 * Main Virtual List Logic Hook
 */

import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import type { RenderState, VirtualListProps } from '../types';
import { ViewportCalculator } from '../utils/calculator';
import { ScrollManager } from '../utils/scroll-manager';

export function useVirtualList(props: VirtualListProps) {
  // 渲染状态
  const renderState = ref<RenderState>({
    visibleStart: 0,
    visibleEnd: 0,
    bufferedStart: 0,
    bufferedEnd: 0,
    scrollTop: 0,
    containerHeight: 0,
    totalHeight: 0,
    isScrolling: false
  });

  // 计算器和滚动管理器实例
  const calculator = ref<ViewportCalculator | null>(null);
  const scrollManager = ref<ScrollManager | null>(null);

  // 容器高度（数值形式）
  const containerHeight = computed(() => {
    if (typeof props.height === 'number') {
      return props.height;
    }
    if (typeof props.height === 'string') {
      const numericValue = parseFloat(props.height);
      return isNaN(numericValue) ? 0 : numericValue;
    }
    return 0;
  });

  // 缓冲区大小
  const bufferSize = computed(() => props.bufferSize || 5);

  // 简化的key生成策略 - 直接使用虚拟索引

  // 可见的列表项
  const visibleItems = computed(() => {
    if (!props.data || props.data.length === 0) {
      return [];
    }

    const { bufferedStart, bufferedEnd, visibleStart, visibleEnd } = renderState.value;
    const dataLength = props.data.length;

    // 更严格的边界检查
    const startIndex = Math.max(0, Math.min(bufferedStart, dataLength));
    const endIndex = Math.max(startIndex, Math.min(bufferedEnd, dataLength));

    // 确保有数据可渲染且范围有效
    if (startIndex >= endIndex || startIndex >= dataLength || endIndex <= 0) {
      return [];
    }

    // 安全地切片数据，确保不会越界
    const safeEndIndex = Math.min(endIndex, dataLength);
    const items = props.data.slice(startIndex, safeEndIndex);

    if (!items || items.length === 0) {
      return [];
    }

    return items.map((item, index) => {
      const virtualIndex = startIndex + index;

      // 确保item是对象，处理各种数据类型
      let safeItem: any;
      if (item && typeof item === 'object') {
        safeItem = { ...item };
      } else {
        safeItem = { value: item, id: virtualIndex };
      }

      // 减少调试日志输出，避免性能问题
      if (Math.random() < 0.01) {
        // 只有1%的概率输出日志
        console.log('VirtualList item render:', { virtualIndex, startIndex, endIndex, dataLength });
      }

      return {
        ...safeItem,
        _virtualIndex: virtualIndex,
        _isVisible: virtualIndex >= visibleStart && virtualIndex < visibleEnd,
        _key: `vl-${virtualIndex}`, // 使用简单稳定的key
        _itemId: `item-${virtualIndex}` // 添加专门的item标识
      };
    });
  });

  // 占位元素样式
  const placeholderStyle = computed(() => ({
    height: `${renderState.value.totalHeight}px`,
    position: 'relative' as const
  }));

  // 可见区域偏移样式
  const visibleAreaStyle = computed(() => {
    const offsetTop = calculator.value?.getItemPosition(renderState.value.bufferedStart)?.top || 0;
    return {
      transform: `translateY(${offsetTop}px)`,
      position: 'relative' as const
    };
  });

  // 更新渲染状态
  const updateRenderState = (newState: Partial<RenderState>) => {
    Object.assign(renderState.value, newState);
  };

  // 计算可视区域和缓冲区
  const calculateRenderRange = () => {
    if (!calculator.value || !props.data || props.data.length === 0) {
      // 重置状态，避免残留数据
      updateRenderState({
        visibleStart: 0,
        visibleEnd: 0,
        bufferedStart: 0,
        bufferedEnd: 0,
        totalHeight: 0,
        scrollTop: 0,
        isScrolling: false
      });
      return;
    }

    const dataLength = props.data.length;
    const visibleRange = calculator.value.getVisibleRange();
    const bufferedRange = calculator.value.getBufferedRange(bufferSize.value);
    const totalHeight = calculator.value.calculateTotalHeight(dataLength);

    // 更严格的边界检查，确保所有索引都在有效范围内
    const visibleStart = Math.max(0, Math.min(visibleRange.start, dataLength));
    const visibleEnd = Math.max(visibleStart, Math.min(visibleRange.end, dataLength));

    // 缓冲区范围计算，确保不会超出数据边界
    let bufferedStart = Math.max(0, Math.min(bufferedRange.start, dataLength));
    let bufferedEnd = Math.max(bufferedStart, Math.min(bufferedRange.end, dataLength));

    // 确保缓冲区至少包含可视区域
    bufferedStart = Math.min(bufferedStart, visibleStart);
    bufferedEnd = Math.max(bufferedEnd, visibleEnd);

    // 最终边界检查，防止越界
    bufferedStart = Math.max(0, Math.min(bufferedStart, dataLength));
    bufferedEnd = Math.max(bufferedStart, Math.min(bufferedEnd, dataLength));

    // 确保至少有一个项目可以渲染（如果有数据的话）
    if (dataLength > 0 && bufferedEnd <= bufferedStart) {
      bufferedEnd = Math.min(bufferedStart + 1, dataLength);
    }

    const newState = {
      visibleStart,
      visibleEnd,
      bufferedStart,
      bufferedEnd,
      totalHeight,
      scrollTop: calculator.value.getScrollTop(),
      isScrolling: scrollManager.value?.getIsScrolling() || false
    };

    updateRenderState(newState);

    // 减少调试信息的输出频率，避免性能问题
    if (Math.random() < 0.05) {
      // 只有5%的概率输出日志
      console.log('VirtualList render range:', {
        visible: `${visibleStart}-${visibleEnd}`,
        buffered: `${bufferedStart}-${bufferedEnd}`,
        total: dataLength,
        scrollTop: calculator.value.getScrollTop(),
        containerHeight: containerHeight.value,
        itemHeight: props.itemHeight
      });
    }
  };

  // 处理数据变化
  const handleDataChange = () => {
    if (!calculator.value) return;

    // 重新计算总高度和渲染范围
    const totalHeight = calculator.value.calculateTotalHeight(props.data.length);
    updateRenderState({ totalHeight });

    // 重新计算可视区域
    calculateRenderRange();
  };

  // 处理容器尺寸变化
  const handleContainerResize = () => {
    if (!calculator.value) return;

    calculator.value.updateContainerHeight(containerHeight.value);
    calculateRenderRange();
  };

  // 处理列表项高度变化
  const handleItemHeightChange = () => {
    if (!calculator.value) return;

    calculator.value.updateItemHeight(props.itemHeight);
    calculateRenderRange();
  };

  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    if (!scrollManager.value) {
      console.warn('useVirtualList: ScrollManager not initialized');
      return;
    }

    if (index < 0 || index >= props.data.length) {
      console.warn('useVirtualList: Invalid scroll index', index);
      return;
    }

    scrollManager.value.scrollToIndex(index);
  };

  // 滚动到顶部
  const scrollToTop = () => {
    if (!scrollManager.value) {
      console.warn('useVirtualList: ScrollManager not initialized');
      return;
    }
    scrollManager.value.scrollToTop();
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (!scrollManager.value) {
      console.warn('useVirtualList: ScrollManager not initialized');
      return;
    }
    scrollManager.value.scrollToBottom();
  };

  // 获取列表项位置信息
  const getItemPosition = (index: number) => {
    if (!calculator.value) return null;
    return calculator.value.getItemPosition(index);
  };

  // 检查索引是否可见
  const isIndexVisible = (index: number) => {
    if (!calculator.value) return false;
    return calculator.value.isIndexVisible(index);
  };

  // 初始化
  const initialize = async () => {
    // 等待DOM更新
    await nextTick();

    // 创建计算器实例
    calculator.value = new ViewportCalculator(containerHeight.value, props.itemHeight);

    // 创建滚动管理器实例
    scrollManager.value = new ScrollManager();
    scrollManager.value.setCalculator(calculator.value as any);

    // 初始化渲染状态
    renderState.value.containerHeight = containerHeight.value;
    calculateRenderRange();

    console.log('useVirtualList: Initialized with', {
      containerHeight: containerHeight.value,
      itemHeight: props.itemHeight,
      dataLength: props.data.length,
      bufferSize: bufferSize.value
    });
  };

  // 监听数据变化
  watch(
    () => props.data,
    () => {
      handleDataChange();
    },
    { deep: true }
  );

  // 监听容器高度变化
  watch(containerHeight, () => {
    handleContainerResize();
  });

  // 监听列表项高度变化
  watch(
    () => props.itemHeight,
    () => {
      handleItemHeightChange();
    }
  );

  // 监听缓冲区大小变化
  watch(bufferSize, () => {
    calculateRenderRange();
  });

  // 组件挂载时初始化
  onMounted(() => {
    initialize();
  });

  // 组件卸载时清理资源
  onUnmounted(() => {
    if (scrollManager.value) {
      scrollManager.value.destroy();
    }
  });

  return {
    // 状态
    renderState,
    visibleItems,
    placeholderStyle,
    visibleAreaStyle,

    // 计算器和管理器
    calculator,
    scrollManager,

    // 方法
    updateRenderState,
    calculateRenderRange,
    scrollToIndex,
    scrollToTop,
    scrollToBottom,
    getItemPosition,
    isIndexVisible,
    initialize,

    // 事件处理
    handleDataChange,
    handleContainerResize,
    handleItemHeightChange
  };
}
