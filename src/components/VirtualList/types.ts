/**
 * VirtualList Component Type Definitions
 * 虚拟列表组件类型定义
 */

// 滚动事件类型
export interface ScrollEvent {
  detail: {
    scrollTop: number;
    scrollLeft: number;
    scrollHeight: number;
    scrollWidth: number;
    deltaX: number;
    deltaY: number;
  };
}

// 虚拟列表Props接口
export interface VirtualListProps {
  // 数据源
  data: Array<any>;
  // 容器尺寸
  width?: string | number;
  height: string | number;
  // 列表项配置
  itemHeight: number;
  dynamicHeight?: boolean;
  // 性能优化
  bufferSize?: number;
  threshold?: number;
  // 滚动配置
  scrollTop?: number;
  enableBackToTop?: boolean;
  // 平台特定
  enhanced?: boolean;
  bounces?: boolean;
  showScrollbar?: boolean;
}

// 虚拟列表Events接口
export interface VirtualListEvents {
  scroll: [event: ScrollEvent];
  scrollToTop: [];
  scrollToBottom: [];
  itemVisible: [index: number, item: any];
  itemHidden: [index: number, item: any];
}

// 虚拟列表Slots接口
export interface VirtualListSlots {
  default: {
    item: any;
    index: number;
    isVisible: boolean;
  };
  empty?: {};
  loading?: {};
}

// 列表项数据模型
export interface VirtualListItem {
  id: string | number;
  [key: string]: any;
}

// 渲染状态模型
export interface RenderState {
  visibleStart: number;
  visibleEnd: number;
  bufferedStart: number;
  bufferedEnd: number;
  scrollTop: number;
  containerHeight: number;
  totalHeight: number;
  isScrolling: boolean;
}

// 平台配置模型
export interface PlatformConfig {
  platform: 'mp-weixin' | 'app' | 'h5';
  useNativeScroll: boolean;
  enableVirtualization: boolean;
  optimizeImages: boolean;
  scrollEventThrottle: number;
}

// 可视区域范围
export interface VisibleRange {
  start: number;
  end: number;
}

// 缓冲区范围
export interface BufferedRange {
  start: number;
  end: number;
}

// 列表项位置信息
export interface ItemPosition {
  top: number;
  height: number;
}

// 错误类型定义
export class VirtualListError extends Error {
  constructor(
    message: string,
    public type: 'data' | 'config' | 'platform' | 'performance'
  ) {
    super(message);
    this.name = 'VirtualListError';
  }
}

export class DataError extends VirtualListError {
  constructor(message: string) {
    super(message, 'data');
  }
}

export class ConfigError extends VirtualListError {
  constructor(message: string) {
    super(message, 'config');
  }
}

export class PlatformError extends VirtualListError {
  constructor(message: string) {
    super(message, 'platform');
  }
}

export class PerformanceError extends VirtualListError {
  constructor(message: string) {
    super(message, 'performance');
  }
}

// 计算器接口
export interface IViewportCalculator {
  getVisibleRange(): VisibleRange;
  getBufferedRange(bufferSize: number): BufferedRange;
  calculateTotalHeight(itemCount: number): number;
  getItemPosition(index: number): ItemPosition;
}

// 滚动管理器接口
export interface IScrollManager {
  handleScroll(event: ScrollEvent): void;
  scrollToIndex(index: number): void;
  scrollToTop(): void;
  scrollToBottom(): void;
}

// 错误处理器接口
export interface IVirtualListErrorHandler {
  handleDataError(error: DataError): void;
  handleConfigError(error: ConfigError): void;
  handlePlatformError(error: PlatformError): void;
  handlePerformanceError(error: PerformanceError): void;
}

// 平台优化策略接口
export interface IOptimizationStrategy {
  initializeOptimizations(): void;
  optimizeScrollPerformance(): void;
  optimizeImageLoading(): void;
  optimizeMemoryUsage(): void;
  cleanup(): void;
}
