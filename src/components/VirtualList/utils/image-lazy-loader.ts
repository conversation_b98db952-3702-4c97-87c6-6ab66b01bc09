/**
 * Image Lazy Loader for VirtualList
 * 虚拟列表图片懒加载工具
 */

export interface ImageLoadOptions {
  src: string;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  priority?: 'high' | 'normal' | 'low';
}

export interface ImageCacheItem {
  url: string;
  loaded: boolean;
  error: boolean;
  loading: boolean;
  loadTime?: number;
  size?: { width: number; height: number };
  retryCount: number;
  lastAccess: number;
}

export interface ImageLoadResult {
  success: boolean;
  url: string;
  size?: { width: number; height: number };
  error?: string;
  fromCache?: boolean;
}

export class ImageLazyLoader {
  private cache = new Map<string, ImageCacheItem>();
  private loadingQueue = new Map<string, Promise<ImageLoadResult>>();
  private preloadQueue: string[] = [];
  private maxConcurrentLoads = 3;
  private currentLoads = 0;
  private maxCacheSize = 100;
  private cacheTimeout = 30 * 60 * 1000; // 30分钟缓存过期

  /**
   * 加载图片
   * @param options 加载选项
   * @returns 加载结果Promise
   */
  async loadImage(options: ImageLoadOptions): Promise<ImageLoadResult> {
    const {
      src,
      timeout = 10000,
      retryCount = 3,
      retryDelay = 1000,
      priority = 'normal'
    } = options;

    // 检查缓存
    const cached = this.getCachedImage(src);
    if (cached) {
      if (cached.loaded) {
        return {
          success: true,
          url: src,
          size: cached.size,
          fromCache: true
        };
      }
      if (cached.error && cached.retryCount >= retryCount) {
        return {
          success: false,
          url: src,
          error: 'Max retry count exceeded',
          fromCache: true
        };
      }
    }

    // 检查是否正在加载
    const existingLoad = this.loadingQueue.get(src);
    if (existingLoad) {
      return existingLoad;
    }

    // 创建加载Promise
    const loadPromise = this.performImageLoad(src, timeout, retryCount, retryDelay, priority);
    this.loadingQueue.set(src, loadPromise);

    try {
      const result = await loadPromise;
      return result;
    } finally {
      this.loadingQueue.delete(src);
    }
  }

  /**
   * 预加载图片
   * @param urls 图片URL数组
   * @param priority 优先级
   */
  async preloadImages(urls: string[], priority: 'high' | 'normal' | 'low' = 'low'): Promise<void> {
    // 过滤已加载或正在加载的图片
    const urlsToLoad = urls.filter((url) => {
      const cached = this.getCachedImage(url);
      return !cached?.loaded && !this.loadingQueue.has(url);
    });

    if (urlsToLoad.length === 0) {
      return;
    }

    // 根据优先级排序
    if (priority === 'high') {
      this.preloadQueue.unshift(...urlsToLoad);
    } else {
      this.preloadQueue.push(...urlsToLoad);
    }

    // 开始预加载
    this.processPreloadQueue();
  }

  /**
   * 获取缓存的图片
   * @param url 图片URL
   * @returns 缓存项或null
   */
  getCachedImage(url: string): ImageCacheItem | null {
    const cached = this.cache.get(url);
    if (!cached) {
      return null;
    }

    // 检查缓存是否过期
    if (Date.now() - cached.lastAccess > this.cacheTimeout) {
      this.cache.delete(url);
      return null;
    }

    // 更新访问时间
    cached.lastAccess = Date.now();
    return cached;
  }

  /**
   * 清理缓存
   * @param force 是否强制清理所有缓存
   */
  clearCache(force = false): void {
    if (force) {
      this.cache.clear();
      console.log('ImageLazyLoader: All cache cleared');
      return;
    }

    const now = Date.now();
    let clearedCount = 0;

    // 清理过期缓存
    for (const [url, item] of this.cache.entries()) {
      if (now - item.lastAccess > this.cacheTimeout) {
        this.cache.delete(url);
        clearedCount++;
      }
    }

    // 如果缓存仍然过大，清理最久未访问的项目
    if (this.cache.size > this.maxCacheSize) {
      const sortedItems = Array.from(this.cache.entries()).sort(
        ([, a], [, b]) => a.lastAccess - b.lastAccess
      );

      const itemsToRemove = sortedItems.slice(0, this.cache.size - this.maxCacheSize);
      itemsToRemove.forEach(([url]) => {
        this.cache.delete(url);
        clearedCount++;
      });
    }

    if (clearedCount > 0) {
      console.log(`ImageLazyLoader: Cleared ${clearedCount} cached items`);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats(): {
    total: number;
    loaded: number;
    errors: number;
    loading: number;
    memoryUsage: number;
  } {
    let loaded = 0;
    let errors = 0;
    let memoryUsage = 0;

    for (const item of this.cache.values()) {
      if (item.loaded) loaded++;
      if (item.error) errors++;
      memoryUsage += this.estimateItemSize(item);
    }

    return {
      total: this.cache.size,
      loaded,
      errors,
      loading: this.loadingQueue.size,
      memoryUsage
    };
  }

  /**
   * 设置最大并发加载数
   * @param max 最大并发数
   */
  setMaxConcurrentLoads(max: number): void {
    this.maxConcurrentLoads = Math.max(1, Math.min(10, max));
  }

  /**
   * 设置最大缓存大小
   * @param size 最大缓存大小
   */
  setMaxCacheSize(size: number): void {
    this.maxCacheSize = Math.max(10, size);
    this.clearCache(); // 立即清理超出的缓存
  }

  /**
   * 执行图片加载
   * @param src 图片URL
   * @param timeout 超时时间
   * @param maxRetries 最大重试次数
   * @param retryDelay 重试延迟
   * @param priority 优先级
   * @returns 加载结果
   */
  private async performImageLoad(
    src: string,
    timeout: number,
    maxRetries: number,
    retryDelay: number,
    priority: string
  ): Promise<ImageLoadResult> {
    // 等待并发控制
    await this.waitForLoadSlot(priority);

    this.currentLoads++;

    try {
      // 更新缓存状态
      const cacheItem: ImageCacheItem = this.cache.get(src) || {
        url: src,
        loaded: false,
        error: false,
        loading: true,
        retryCount: 0,
        lastAccess: Date.now()
      };

      cacheItem.loading = true;
      this.cache.set(src, cacheItem);

      let lastError = '';

      // 重试循环
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          const result = await this.loadSingleImage(src, timeout);

          // 更新缓存 - 成功
          cacheItem.loaded = true;
          cacheItem.error = false;
          cacheItem.loading = false;
          cacheItem.loadTime = Date.now();
          cacheItem.size = result.size;
          cacheItem.lastAccess = Date.now();

          return {
            success: true,
            url: src,
            size: result.size
          };
        } catch (error) {
          lastError = error instanceof Error ? error.message : 'Unknown error';
          cacheItem.retryCount = attempt + 1;

          // 如果不是最后一次尝试，等待后重试
          if (attempt < maxRetries) {
            await this.delay(retryDelay * 2 ** attempt); // 指数退避
          }
        }
      }

      // 所有重试都失败了
      cacheItem.loaded = false;
      cacheItem.error = true;
      cacheItem.loading = false;
      cacheItem.lastAccess = Date.now();

      return {
        success: false,
        url: src,
        error: lastError
      };
    } finally {
      this.currentLoads--;
      this.processPreloadQueue(); // 处理预加载队列
    }
  }

  /**
   * 加载单个图片
   * @param src 图片URL
   * @param timeout 超时时间
   * @returns 加载结果
   */
  private loadSingleImage(
    src: string,
    timeout: number
  ): Promise<{ size?: { width: number; height: number } }> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Image load timeout'));
      }, timeout);

      // 使用uni.getImageInfo进行预加载
      uni.getImageInfo({
        src,
        success: (res) => {
          clearTimeout(timer);
          resolve({
            size: {
              width: res.width,
              height: res.height
            }
          });
        },
        fail: (error) => {
          clearTimeout(timer);
          reject(new Error(error.errMsg || 'Image load failed'));
        }
      });
    });
  }

  /**
   * 等待加载槽位
   * @param priority 优先级
   */
  private async waitForLoadSlot(priority: string): Promise<void> {
    // 高优先级可以立即开始
    if (priority === 'high' || this.currentLoads < this.maxConcurrentLoads) {
      return;
    }

    // 等待有空闲槽位
    return new Promise((resolve) => {
      const checkSlot = () => {
        if (this.currentLoads < this.maxConcurrentLoads) {
          resolve();
        } else {
          setTimeout(checkSlot, 100);
        }
      };
      checkSlot();
    });
  }

  /**
   * 处理预加载队列
   */
  private processPreloadQueue(): void {
    while (this.preloadQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
      const url = this.preloadQueue.shift();
      if (url && !this.loadingQueue.has(url)) {
        this.loadImage({ src: url, priority: 'low' }).catch((error) => {
          console.warn('Preload failed:', url, error);
        });
      }
    }
  }

  /**
   * 延迟函数
   * @param ms 延迟毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 估算缓存项大小
   * @param item 缓存项
   * @returns 估算大小（字节）
   */
  private estimateItemSize(item: ImageCacheItem): number {
    let size = 200; // 基础对象大小
    size += item.url.length * 2; // URL字符串
    if (item.size) {
      size += 16; // 尺寸对象
    }
    return size;
  }
}

// 创建全局实例
export const imageLoader = new ImageLazyLoader();

// 配置优化参数
imageLoader.setMaxConcurrentLoads(3);
imageLoader.setMaxCacheSize(50);
