/**
 * ScrollManager Unit Tests
 * 滚动管理器单元测试
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ScrollManager } from '../scroll-manager';
import { ViewportCalculator } from '../calculator';
import type { ScrollEvent } from '../../types';

describe('ScrollManager', () => {
  let scrollManager: ScrollManager;
  let calculator: ViewportCalculator;
  let mockScrollToCallback: ReturnType<typeof vi.fn>;
  let mockEventCallbacks: {
    onScroll: ReturnType<typeof vi.fn>;
    onScrollToTop: ReturnType<typeof vi.fn>;
    onScrollToBottom: ReturnType<typeof vi.fn>;
  };

  const containerHeight = 400;
  const itemHeight = 50;

  beforeEach(() => {
    scrollManager = new ScrollManager();
    calculator = new ViewportCalculator(containerHeight, itemHeight);

    mockScrollToCallback = vi.fn();
    mockEventCallbacks = {
      onScroll: vi.fn(),
      onScrollToTop: vi.fn(),
      onScrollToBottom: vi.fn(),
    };

    scrollManager.setCalculator(calculator);
    scrollManager.setScrollToCallback(mockScrollToCallback);
    scrollManager.setEventCallbacks(mockEventCallbacks);
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      const newScrollManager = new ScrollManager();
      expect(newScrollManager.getScrollTop()).toBe(0);
      expect(newScrollManager.getIsScrolling()).toBe(false);
    });
  });

  describe('setCalculator', () => {
    it('should set calculator instance', () => {
      const newScrollManager = new ScrollManager();
      newScrollManager.setCalculator(calculator);

      // Test by calling a method that requires calculator
      newScrollManager.scrollToIndex(5);
      // Should not log warning about calculator not set
    });
  });

  describe('setScrollToCallback', () => {
    it('should set scroll callback', () => {
      const callback = vi.fn();
      scrollManager.setScrollToCallback(callback);

      scrollManager.scrollToTop();
      expect(callback).toHaveBeenCalledWith(0);
    });
  });

  describe('setEventCallbacks', () => {
    it('should set event callbacks', () => {
      const callbacks = {
        onScroll: vi.fn(),
        onScrollToTop: vi.fn(),
        onScrollToBottom: vi.fn(),
      };

      scrollManager.setEventCallbacks(callbacks);

      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 0,
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);
      expect(callbacks.onScroll).toHaveBeenCalledWith(scrollEvent);
      expect(callbacks.onScrollToTop).toHaveBeenCalled();
    });
  });

  describe('handleScroll', () => {
    it('should update scroll position and calculator', () => {
      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 200,
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);

      expect(scrollManager.getScrollTop()).toBe(200);
      expect(calculator.getScrollTop()).toBe(200);
      expect(scrollManager.getIsScrolling()).toBe(true);
      expect(mockEventCallbacks.onScroll).toHaveBeenCalledWith(scrollEvent);
    });

    it('should trigger onScrollToTop when at top', () => {
      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 0,
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);
      expect(mockEventCallbacks.onScrollToTop).toHaveBeenCalled();
    });

    it('should trigger onScrollToBottom when at bottom', () => {
      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 590, // 1000 - 400 = 600, with 10px tolerance
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);
      expect(mockEventCallbacks.onScrollToBottom).toHaveBeenCalled();
    });

    it('should set isScrolling to false after timeout', (done) => {
      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 100,
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);
      expect(scrollManager.getIsScrolling()).toBe(true);

      setTimeout(() => {
        expect(scrollManager.getIsScrolling()).toBe(false);
        done();
      }, 200);
    });
  });

  describe('scrollToIndex', () => {
    it('should scroll to correct position for index', () => {
      const index = 10;
      scrollManager.scrollToIndex(index);

      const expectedScrollTop = index * itemHeight; // 10 * 50 = 500
      expect(mockScrollToCallback).toHaveBeenCalledWith(expectedScrollTop);
    });

    it('should handle negative index', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

      scrollManager.scrollToIndex(-5);
      expect(consoleSpy).toHaveBeenCalledWith('ScrollManager: Invalid index, cannot be negative');
      expect(mockScrollToCallback).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should warn when calculator not set', () => {
      const newScrollManager = new ScrollManager();
      newScrollManager.setScrollToCallback(mockScrollToCallback);

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

      newScrollManager.scrollToIndex(5);
      expect(consoleSpy).toHaveBeenCalledWith('ScrollManager: Calculator not set, cannot scroll to index');

      consoleSpy.mockRestore();
    });
  });

  describe('scrollToTop', () => {
    it('should scroll to position 0', () => {
      scrollManager.scrollToTop();
      expect(mockScrollToCallback).toHaveBeenCalledWith(0);
    });
  });

  describe('scrollToBottom', () => {
    it('should scroll to bottom position', () => {
      scrollManager.scrollToBottom();

      // Should call with a large scroll position
      expect(mockScrollToCallback).toHaveBeenCalled();
      const callArgs = mockScrollToCallback.mock.calls[0];
      expect(callArgs[0]).toBeGreaterThan(0);
    });

    it('should warn when calculator not set', () => {
      const newScrollManager = new ScrollManager();
      newScrollManager.setScrollToCallback(mockScrollToCallback);

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

      newScrollManager.scrollToBottom();
      expect(consoleSpy).toHaveBeenCalledWith('ScrollManager: Calculator not set, cannot scroll to bottom');

      consoleSpy.mockRestore();
    });
  });

  describe('setScrollTop', () => {
    it('should set scroll position without animation', () => {
      scrollManager.setScrollTop(300);

      expect(scrollManager.getScrollTop()).toBe(300);
      expect(calculator.getScrollTop()).toBe(300);
    });

    it('should not allow negative scroll position', () => {
      scrollManager.setScrollTop(-100);
      expect(scrollManager.getScrollTop()).toBe(0);
    });
  });

  describe('getScrollProgress', () => {
    it('should return correct progress', () => {
      // Mock a scenario with known total height
      const itemCount = 20; // 20 * 50 = 1000px total
      const totalHeight = itemCount * itemHeight;
      const maxScrollTop = totalHeight - containerHeight; // 1000 - 400 = 600

      scrollManager.setScrollTop(300); // Half way

      // Progress should be 300 / 600 = 0.5
      const progress = scrollManager.getScrollProgress();
      expect(progress).toBeCloseTo(0.5, 2);
    });

    it('should return 0 when calculator not set', () => {
      const newScrollManager = new ScrollManager();
      expect(newScrollManager.getScrollProgress()).toBe(0);
    });
  });

  describe('canScrollUp', () => {
    it('should return true when not at top', () => {
      scrollManager.setScrollTop(100);
      expect(scrollManager.canScrollUp()).toBe(true);
    });

    it('should return false when at top', () => {
      scrollManager.setScrollTop(0);
      expect(scrollManager.canScrollUp()).toBe(false);
    });
  });

  describe('canScrollDown', () => {
    it('should return false when calculator not set', () => {
      const newScrollManager = new ScrollManager();
      expect(newScrollManager.canScrollDown()).toBe(false);
    });

    it('should return correct value based on scroll position', () => {
      scrollManager.setScrollTop(0);
      expect(scrollManager.canScrollDown()).toBe(true);
    });
  });

  describe('smoothScrollTo', () => {
    it('should initiate smooth scroll animation', () => {
      const targetScrollTop = 500;

      // Mock requestAnimationFrame
      const mockRAF = vi.fn((callback) => {
        callback();
        return 1;
      });
      global.requestAnimationFrame = mockRAF;

      scrollManager.smoothScrollTo(targetScrollTop, 100);

      expect(mockRAF).toHaveBeenCalled();
      expect(mockScrollToCallback).toHaveBeenCalled();
    });
  });

  describe('easeInOutCubic', () => {
    it('should apply correct easing', () => {
      // Test the private method through smoothScrollTo
      const targetScrollTop = 400;
      scrollManager.setScrollTop(0);

      const mockRAF = vi.fn((callback) => {
        callback();
        return 1;
      });
      global.requestAnimationFrame = mockRAF;

      scrollManager.smoothScrollTo(targetScrollTop, 100);

      // Should have called the scroll callback
      expect(mockScrollToCallback).toHaveBeenCalled();
    });
  });

  describe('destroy', () => {
    it('should clean up resources', () => {
      scrollManager.setScrollTop(100);

      scrollManager.destroy();

      // After destroy, calculator should be null
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
      scrollManager.scrollToIndex(5);
      expect(consoleSpy).toHaveBeenCalledWith('ScrollManager: Calculator not set, cannot scroll to index');
      consoleSpy.mockRestore();
    });

    it('should clear scroll timer', () => {
      const scrollEvent: ScrollEvent = {
        detail: {
          scrollTop: 100,
          scrollLeft: 0,
          scrollHeight: 1000,
          scrollWidth: 400,
          deltaX: 0,
          deltaY: 0,
        },
      };

      scrollManager.handleScroll(scrollEvent);
      expect(scrollManager.getIsScrolling()).toBe(true);

      scrollManager.destroy();

      // Timer should be cleared, but isScrolling might still be true
      // since destroy doesn't reset the flag immediately
    });
  });
});