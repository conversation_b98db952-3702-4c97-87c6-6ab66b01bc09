/**
 * Platform Adapter Tests
 * 平台适配器测试
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  PlatformConfigManager,
  PlatformDetector,
  PlatformScrollImplementation,
  getCurrentPlatform,
  getCurrentPlatformConfig,
  isApp,
  isH5,
  isMiniProgram
} from '../platform-adapter';

// Mock uni object for testing
const mockUni = {
  getSystemInfoSync: vi.fn(),
  createIntersectionObserver: vi.fn()
};

// Mock window object for H5 testing
const mockWindow = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  IntersectionObserver: vi.fn()
};

describe('PlatformDetector', () => {
  let detector: PlatformDetector;

  beforeEach(() => {
    // Reset singleton instance
    (PlatformDetector as any).instance = null;
    detector = PlatformDetector.getInstance();

    // Clear cached platform
    (detector as any)._platform = null;
  });

  it('should detect miniprogram platform', () => {
    // Mock uni environment for miniprogram
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({
        platform: 'devtools',
        environment: 'miniprogram'
      })
    };

    const platform = detector.detectPlatform();
    expect(platform).toBe('mp-weixin');
    expect(detector.isMiniProgram()).toBe(true);
    expect(detector.isApp()).toBe(false);
    expect(detector.isH5()).toBe(false);
  });

  it('should detect app platform', () => {
    // Mock uni environment for app
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({
        platform: 'android',
        environment: 'app'
      })
    };

    const platform = detector.detectPlatform();
    expect(platform).toBe('app');
    expect(detector.isApp()).toBe(true);
    expect(detector.isMiniProgram()).toBe(false);
    expect(detector.isH5()).toBe(false);
  });

  it('should detect h5 platform', () => {
    // Mock uni environment for h5
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({
        platform: 'h5'
      })
    };
    (global as any).window = mockWindow;

    const platform = detector.detectPlatform();
    expect(platform).toBe('h5');
    expect(detector.isH5()).toBe(true);
    expect(detector.isMiniProgram()).toBe(false);
    expect(detector.isApp()).toBe(false);
  });

  it('should detect unknown platform when no uni object', () => {
    delete (global as any).uni;
    delete (global as any).window;

    const platform = detector.detectPlatform();
    expect(platform).toBe('unknown');
  });

  it('should cache platform detection result', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: vi.fn(() => ({
        platform: 'devtools',
        environment: 'miniprogram'
      }))
    };

    // First call
    detector.detectPlatform();
    expect(mockUni.getSystemInfoSync).toHaveBeenCalledTimes(1);

    // Second call should use cached result
    detector.detectPlatform();
    expect(mockUni.getSystemInfoSync).toHaveBeenCalledTimes(1);
  });

  it('should check intersection observer support', () => {
    // Test miniprogram support
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' }),
      createIntersectionObserver: vi.fn()
    };
    (detector as any)._platform = 'mp-weixin';
    expect(detector.supportsIntersectionObserver()).toBe(true);

    // Test H5 support
    (global as any).window = { IntersectionObserver: vi.fn() };
    (detector as any)._platform = 'h5';
    expect(detector.supportsIntersectionObserver()).toBe(true);

    // Test no support
    delete (global as any).window;
    (detector as any)._platform = 'unknown';
    expect(detector.supportsIntersectionObserver()).toBe(false);
  });
});

describe('PlatformConfigManager', () => {
  let configManager: PlatformConfigManager;

  beforeEach(() => {
    // Reset singleton instance
    (PlatformConfigManager as any).instance = null;
    (PlatformDetector as any).instance = null;
    configManager = PlatformConfigManager.getInstance();
  });

  it('should return miniprogram config', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
    };

    const config = configManager.getCurrentConfig();
    expect(config.enhanced).toBe(true);
    expect(config.useNativeScroll).toBe(true);
    expect(config.enableVirtualization).toBe(true);
    expect(config.bufferSizeMultiplier).toBe(1.5);
  });

  it('should return app config', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'android', environment: 'app' })
    };

    const config = configManager.getCurrentConfig();
    expect(config.useNativeScroll).toBe(true);
    expect(config.enableVirtualization).toBe(true);
    expect(config.bufferSizeMultiplier).toBe(2.0);
    expect(config.scrollEventThrottle).toBe(8);
  });

  it('should return h5 config', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'h5' })
    };
    (global as any).window = mockWindow;

    const config = configManager.getCurrentConfig();
    expect(config.useNativeScroll).toBe(true);
    expect(config.enableVirtualization).toBe(true);
    expect(config.showScrollbar).toBe(true);
    expect(config.bufferSizeMultiplier).toBe(1.0);
  });

  it('should update platform config', () => {
    const originalConfig = configManager.getConfigForPlatform('h5');
    const originalThrottle = originalConfig.scrollEventThrottle;

    configManager.updateConfig('h5', { scrollEventThrottle: 32 });

    const updatedConfig = configManager.getConfigForPlatform('h5');
    expect(updatedConfig.scrollEventThrottle).toBe(32);
    expect(updatedConfig.scrollEventThrottle).not.toBe(originalThrottle);
  });

  it('should generate platform config', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
    };

    const platformConfig = configManager.generatePlatformConfig();
    expect(platformConfig.platform).toBe('mp-weixin');
    expect(platformConfig.useNativeScroll).toBe(true);
    expect(platformConfig.enableVirtualization).toBe(true);
    expect(platformConfig.optimizeImages).toBe(true);
  });
});

describe('PlatformScrollImplementation', () => {
  let scrollImpl: PlatformScrollImplementation;

  beforeEach(() => {
    (PlatformDetector as any).instance = null;
    (PlatformConfigManager as any).instance = null;
    scrollImpl = new PlatformScrollImplementation();
  });

  it('should return miniprogram scroll view props', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
    };

    const props = scrollImpl.getScrollViewProps();
    expect(props['scroll-y']).toBe(true);
    expect(props.enhanced).toBe(true);
    expect(props.bounces).toBe(true);
    expect(props['enable-passive']).toBe(true);
    expect(props['enable-back-to-top']).toBe(true);
  });

  it('should return app scroll view props', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'android', environment: 'app' })
    };

    const props = scrollImpl.getScrollViewProps();
    expect(props['scroll-y']).toBe(true);
    expect(props.bounces).toBe(true);
    expect(props['enable-back-to-top']).toBe(true);
    expect(props.enhanced).toBeUndefined();
  });

  it('should return h5 scroll view props', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'h5' })
    };
    (global as any).window = mockWindow;

    const props = scrollImpl.getScrollViewProps();
    expect(props['scroll-y']).toBe(true);
    expect(props['show-scrollbar']).toBe(true);
    expect(props.enhanced).toBeUndefined();
    expect(props.bounces).toBeUndefined();
  });

  it('should return scroll event config', () => {
    const config = scrollImpl.getScrollEventConfig();
    expect(config.eventName).toBe('scroll');
    expect(typeof config.throttle).toBe('number');
    expect(config.throttle).toBeGreaterThan(0);
  });

  it('should return platform styles', () => {
    const styles = scrollImpl.getPlatformStyles();
    expect(typeof styles).toBe('object');
    expect(styles.transform).toBe('translateZ(0)');
    expect(styles.willChange).toBe('transform');
  });
});

describe('Convenience functions', () => {
  beforeEach(() => {
    (PlatformDetector as any).instance = null;
    (PlatformConfigManager as any).instance = null;
  });

  it('should return current platform', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
    };

    expect(getCurrentPlatform()).toBe('mp-weixin');
    expect(isMiniProgram()).toBe(true);
    expect(isApp()).toBe(false);
    expect(isH5()).toBe(false);
  });

  it('should return current platform config', () => {
    (global as any).uni = {
      ...mockUni,
      getSystemInfoSync: () => ({ platform: 'android', environment: 'app' })
    };

    const config = getCurrentPlatformConfig();
    expect(config.useNativeScroll).toBe(true);
    expect(config.bufferSizeMultiplier).toBe(2.0);
  });
});
