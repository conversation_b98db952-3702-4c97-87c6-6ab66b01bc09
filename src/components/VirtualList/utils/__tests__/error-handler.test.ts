/**
 * VirtualList Error Handler Tests
 * 虚拟列表错误处理器测试
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { VirtualListErrorHandler } from '../error-handler';
import type { VirtualListProps } from '../../types';
import { ConfigError, DataError, PerformanceError, PlatformError } from '../../types';

// Mock uni API
const mockUni = {
    showToast: vi.fn()
};
(global as any).uni = mockUni;

describe('VirtualListErrorHandler', () => {
    let errorHandler: VirtualListErrorHandler;

    beforeEach(() => {
        errorHandler = new VirtualListErrorHandler();
        errorHandler.resetErrorCount();
        vi.clearAllMocks();
    });

    describe('Data Validation', () => {
        it('should validate valid data array', () => {
            const validData = [
                { id: 1, name: 'Item 1' },
                { id: 2, name: 'Item 2' },
                { id: 3, name: 'Item 3' }
            ];

            const result = errorHandler.validateData(validData);
            expect(result.isValid).toBe(true);
            expect(result.error).toBeUndefined();
        });

        it('should reject non-array data', () => {
            const invalidData = 'not an array' as any;

            const result = errorHandler.validateData(invalidData);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(DataError);
            expect(result.error?.message).toBe('Data must be an array');
        });

        it('should reject empty array', () => {
            const emptyData: any[] = [];

            const result = errorHandler.validateData(emptyData);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(DataError);
            expect(result.error?.message).toBe('Data array is empty');
        });

        it('should reject array with null items', () => {
            const dataWithNull = [{ id: 1 }, null, { id: 3 }];

            const result = errorHandler.validateData(dataWithNull);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(DataError);
            expect(result.error?.message).toContain('Invalid item at index 1');
        });

        it('should reject array with undefined items', () => {
            const dataWithUndefined = [{ id: 1 }, undefined, { id: 3 }];

            const result = errorHandler.validateData(dataWithUndefined);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(DataError);
            expect(result.error?.message).toContain('Invalid item at index 1');
        });
    });

    describe('Config Validation', () => {
        it('should validate valid config', () => {
            const validConfig: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: 10,
                threshold: 50
            };

            const result = errorHandler.validateConfig(validConfig);
            expect(result.isValid).toBe(true);
            expect(result.error).toBeUndefined();
        });

        it('should reject config without height', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                itemHeight: 60
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
            expect(result.error?.message).toContain('Height must be a positive number');
        });

        it('should reject config with zero height', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                height: 0,
                itemHeight: 60
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
        });

        it('should reject config without itemHeight', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                height: '400px'
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
            expect(result.error?.message).toContain('ItemHeight must be a positive number');
        });

        it('should reject config with negative bufferSize', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: -5
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
            expect(result.error?.message).toContain('BufferSize must be between 0 and 100');
        });

        it('should reject config with excessive bufferSize', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: 150
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
        });

        it('should reject config with invalid threshold', () => {
            const invalidConfig: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                threshold: -10
            };

            const result = errorHandler.validateConfig(invalidConfig);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
            expect(result.error?.message).toContain('Threshold must be between 0 and 1000');
        });
    });

    describe('Error Handling', () => {
        it('should handle data errors', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            const dataError = new DataError('Test data error');

            errorHandler.handleDataError(dataError);

            expect(consoleSpy).toHaveBeenCalledWith(
                'VirtualList: Data error detected',
                expect.objectContaining({
                    message: 'Test data error',
                    type: 'data'
                })
            );
            expect(mockUni.showToast).toHaveBeenCalledWith({
                title: '数据加载异常，请检查数据格式',
                icon: 'none',
                duration: 3000
            });

            consoleSpy.mockRestore();
        });

        it('should handle config errors', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            const configError = new ConfigError('Test config error');

            errorHandler.handleConfigError(configError);

            expect(consoleSpy).toHaveBeenCalledWith(
                'VirtualList: Configuration error detected',
                expect.objectContaining({
                    message: 'Test config error',
                    type: 'config'
                })
            );
            expect(mockUni.showToast).toHaveBeenCalledWith({
                title: '配置参数异常，已使用默认配置',
                icon: 'none',
                duration: 3000
            });

            consoleSpy.mockRestore();
        });

        it('should handle platform errors', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            const platformError = new PlatformError('Test platform error');

            errorHandler.handlePlatformError(platformError);

            expect(consoleSpy).toHaveBeenCalledWith(
                'VirtualList: Platform compatibility error detected',
                expect.objectContaining({
                    message: 'Test platform error',
                    type: 'platform'
                })
            );
            expect(mockUni.showToast).toHaveBeenCalledWith({
                title: '当前平台兼容性问题，已启用兼容模式',
                icon: 'none',
                duration: 3000
            });

            consoleSpy.mockRestore();
        });

        it('should handle performance errors', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            const performanceError = new PerformanceError('Test performance error');

            errorHandler.handlePerformanceError(performanceError);

            expect(consoleSpy).toHaveBeenCalledWith(
                'VirtualList: Performance issue detected',
                expect.objectContaining({
                    message: 'Test performance error',
                    type: 'performance'
                })
            );
            expect(mockUni.showToast).toHaveBeenCalledWith({
                title: '检测到性能问题，已自动优化',
                icon: 'none',
                duration: 3000
            });

            consoleSpy.mockRestore();
        });
    });

    describe('Default Configuration', () => {
        it('should provide default configuration', () => {
            const defaultConfig = errorHandler.getDefaultConfig();

            expect(defaultConfig).toEqual({
                height: '300px',
                itemHeight: 50,
                bufferSize: 5,
                threshold: 100,
                dynamicHeight: false,
                enableBackToTop: true,
                enhanced: true,
                bounces: true,
                showScrollbar: true
            });
        });
    });

    describe('Error Statistics', () => {
        it('should track error count', () => {
            const dataError = new DataError('Test error');

            errorHandler.handleDataError(dataError);
            errorHandler.handleDataError(dataError);

            const stats = errorHandler.getErrorStats();
            expect(stats.errorCount).toBe(2);
            expect(stats.fallbackMode).toBe(false);
        });

        it('should enable fallback mode after max errors', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            const dataError = new DataError('Test error');

            errorHandler.setMaxErrors(3);

            // Trigger 4 errors to exceed the limit
            for (let i = 0; i < 4; i++) {
                errorHandler.handleDataError(dataError);
            }

            const stats = errorHandler.getErrorStats();
            expect(stats.errorCount).toBe(4);
            expect(stats.fallbackMode).toBe(true);

            consoleSpy.mockRestore();
        });

        it('should reset error count', () => {
            const dataError = new DataError('Test error');

            errorHandler.handleDataError(dataError);
            errorHandler.handleDataError(dataError);

            let stats = errorHandler.getErrorStats();
            expect(stats.errorCount).toBe(2);

            errorHandler.resetErrorCount();

            stats = errorHandler.getErrorStats();
            expect(stats.errorCount).toBe(0);
            expect(stats.fallbackMode).toBe(false);
        });
    });

    describe('Edge Cases', () => {
        it('should handle validation errors gracefully', () => {
            // Test with data that throws during validation
            const problematicData = new Proxy([], {
                get() {
                    throw new Error('Proxy error');
                }
            });

            const result = errorHandler.validateData(problematicData as any);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(DataError);
            expect(result.error?.message).toContain('Data validation failed');
        });

        it('should handle config validation errors gracefully', () => {
            // Test with config that throws during validation
            const problematicConfig = new Proxy(
                {},
                {
                    get() {
                        throw new Error('Proxy error');
                    }
                }
            );

            const result = errorHandler.validateConfig(problematicConfig as any);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeInstanceOf(ConfigError);
            expect(result.error?.message).toContain('Config validation failed');
        });
    });
});
