/**
 * VirtualList Data Validator Tests
 * 虚拟列表数据验证器测试
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { VirtualListDataValidator } from '../data-validator';
import { VirtualListErrorHandler } from '../error-handler';
import type { VirtualListProps } from '../../types';

describe('VirtualListDataValidator', () => {
    let validator: VirtualListDataValidator;
    let mockErrorHandler: VirtualListErrorHandler;

    beforeEach(() => {
        mockErrorHandler = new VirtualListErrorHandler();
        vi.spyOn(mockErrorHandler, 'handleDataError').mockImplementation(() => { });
        vi.spyOn(mockErrorHandler, 'handleConfigError').mockImplementation(() => { });

        validator = new VirtualListDataValidator(mockErrorHandler);
    });

    describe('Data Validation and Normalization', () => {
        it('should handle null data', () => {
            const result = validator.validateAndNormalizeData(null);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toEqual([]);
        });

        it('should handle undefined data', () => {
            const result = validator.validateAndNormalizeData(undefined);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toEqual([]);
        });

        it('should handle empty array', () => {
            const result = validator.validateAndNormalizeData([]);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toEqual([]);
        });

        it('should handle single item array', () => {
            const data = [{ id: 1, name: 'Item 1' }];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toHaveLength(1);
            expect(result.normalizedData![0]).toMatchObject({
                id: 1,
                name: 'Item 1',
                _virtualIndex: 0,
                _isVisible: false
            });
        });

        it('should handle multiple items array', () => {
            const data = [
                { id: 1, name: 'Item 1' },
                { id: 2, name: 'Item 2' },
                { id: 3, name: 'Item 3' }
            ];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toHaveLength(3);
            expect(result.normalizedData![1]).toMatchObject({
                id: 2,
                name: 'Item 2',
                _virtualIndex: 1,
                _isVisible: false
            });
        });

        it('should convert non-array object to array', () => {
            const data = { a: 'value1', b: 'value2' };
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toHaveLength(2);
            expect(result.normalizedData![0].value).toBe('value1');
        });

        it('should convert primitive to single item array', () => {
            const result = validator.validateAndNormalizeData('single item');

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toHaveLength(1);
            expect(result.normalizedData![0]).toMatchObject({
                id: 'virtual-item-0',
                value: 'single item',
                _virtualIndex: 0,
                _isVisible: false
            });
        });

        it('should handle array with null items', () => {
            const data = [{ id: 1 }, null, { id: 3 }];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(false);
            expect(result.error?.message).toContain('Invalid item at index 1');
            expect(result.normalizedData).toEqual([]);
            expect(mockErrorHandler.handleDataError).toHaveBeenCalled();
        });

        it('should handle array with undefined items', () => {
            const data = [{ id: 1 }, undefined, { id: 3 }];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(false);
            expect(result.error?.message).toContain('Invalid item at index 1');
            expect(mockErrorHandler.handleDataError).toHaveBeenCalled();
        });

        it('should generate IDs for items without ID', () => {
            const data = [{ name: 'Item 1' }, { key: 'item2', name: 'Item 2' }, 'primitive item'];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData![0].id).toBe('virtual-item-0');
            expect(result.normalizedData![1].id).toBe('item2'); // Uses key as ID
            expect(result.normalizedData![2].id).toBe('virtual-item-2');
        });

        it('should handle validation errors gracefully', () => {
            // Create data that will throw during processing
            const problematicData = new Proxy([], {
                get() {
                    throw new Error('Proxy error');
                }
            });

            const result = validator.validateAndNormalizeData(problematicData);

            expect(result.isValid).toBe(false);
            expect(result.error?.message).toContain('Data validation failed');
            expect(result.normalizedData).toEqual([]);
            expect(mockErrorHandler.handleDataError).toHaveBeenCalled();
        });
    });

    describe('Config Validation and Normalization', () => {
        it('should validate valid config', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: 10,
                threshold: 50
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig).toEqual(config);
        });

        it('should fix missing height', () => {
            const config: Partial<VirtualListProps> = {
                itemHeight: 60
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.height).toBe('300px');
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should fix negative height', () => {
            const config: Partial<VirtualListProps> = {
                height: -100,
                itemHeight: 60
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.height).toBe(100);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should fix missing itemHeight', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px'
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.itemHeight).toBe(50);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should fix negative bufferSize', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: -5
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.bufferSize).toBe(0);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should cap excessive bufferSize', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: 150
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.bufferSize).toBe(100);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should fix negative threshold', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                threshold: -10
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.threshold).toBe(0);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should cap excessive threshold', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                threshold: 2000
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.threshold).toBe(1000);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should fix negative scrollTop', () => {
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                scrollTop: -50
            };

            const result = validator.validateAndNormalizeConfig(config);

            expect(result.isValid).toBe(true);
            expect(result.normalizedConfig!.scrollTop).toBe(0);
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });

        it('should handle config validation errors gracefully', () => {
            const problematicConfig = new Proxy(
                {},
                {
                    get() {
                        throw new Error('Proxy error');
                    }
                }
            );

            const result = validator.validateAndNormalizeConfig(problematicConfig);

            expect(result.isValid).toBe(false);
            expect(result.error?.message).toContain('Config validation failed');
            expect(result.normalizedConfig).toBeDefined();
            expect(mockErrorHandler.handleConfigError).toHaveBeenCalled();
        });
    });

    describe('Edge Cases Handling', () => {
        it('should disable virtualization for small datasets', () => {
            const data = Array.from({ length: 5 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.shouldUseVirtualization).toBe(false);
            expect(result.warnings).toContain(
                'Small dataset detected, virtualization disabled for better performance'
            );
        });

        it('should optimize config for large datasets', () => {
            const data = Array.from({ length: 15000 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 60,
                bufferSize: 2,
                threshold: 10
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.shouldUseVirtualization).toBe(true);
            expect(result.optimizedConfig.bufferSize).toBe(10);
            expect(result.optimizedConfig.threshold).toBe(50);
            expect(result.warnings).toContain('Large dataset detected, buffer size increased');
            expect(result.warnings).toContain('Large dataset detected, scroll threshold increased');
        });

        it('should warn about very small item height', () => {
            const data = Array.from({ length: 100 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 10
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.warnings).toContain('Very small item height detected, may affect performance');
        });

        it('should optimize for very large item height', () => {
            const data = Array.from({ length: 100 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: '400px',
                itemHeight: 600,
                bufferSize: 10
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.optimizedConfig.bufferSize).toBe(3);
            expect(result.warnings).toContain(
                'Very large item height detected, consider reducing buffer size'
            );
        });

        it('should disable virtualization when all items fit in viewport', () => {
            const data = Array.from({ length: 5 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: 400,
                itemHeight: 60
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.shouldUseVirtualization).toBe(false);
            expect(result.warnings).toContain('All items fit in viewport, virtualization disabled');
        });

        it('should warn about container height too small for item height', () => {
            const data = Array.from({ length: 100 }, (_, i) => ({ id: i }));
            const config: Partial<VirtualListProps> = {
                height: 50,
                itemHeight: 60
            };

            const result = validator.handleEdgeCases(data, config);

            expect(result.warnings).toContain(
                'Container height too small for item height, may cause display issues'
            );
        });
    });

    describe('Data Statistics', () => {
        it('should return empty stats for empty data', () => {
            const stats = validator.getDataStats([]);

            expect(stats).toEqual({
                totalItems: 0,
                hasNullItems: false,
                hasUndefinedItems: false,
                hasObjectItems: false,
                hasPrimitiveItems: false,
                averageItemSize: 0
            });
        });

        it('should return correct stats for mixed data', () => {
            const data = [{ id: 1, name: 'Object' }, 'string', 42, null, undefined, true];

            const stats = validator.getDataStats(data);

            expect(stats.totalItems).toBe(6);
            expect(stats.hasNullItems).toBe(true);
            expect(stats.hasUndefinedItems).toBe(true);
            expect(stats.hasObjectItems).toBe(true);
            expect(stats.hasPrimitiveItems).toBe(true);
            expect(stats.averageItemSize).toBeGreaterThan(0);
        });

        it('should handle non-array input', () => {
            const stats = validator.getDataStats('not an array' as any);

            expect(stats.totalItems).toBe(0);
        });
    });

    describe('Circular Reference Detection', () => {
        it('should detect circular references', () => {
            const obj: any = { id: 1 };
            obj.self = obj; // Create circular reference

            const data = [obj];
            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(false);
            expect(result.error?.message).toContain('Circular reference detected');
            expect(mockErrorHandler.handleDataError).toHaveBeenCalled();
        });

        it('should handle objects without circular references', () => {
            const data = [
                { id: 1, nested: { value: 'test' } },
                { id: 2, array: [1, 2, 3] }
            ];

            const result = validator.validateAndNormalizeData(data);

            expect(result.isValid).toBe(true);
            expect(result.normalizedData).toHaveLength(2);
        });
    });
});
