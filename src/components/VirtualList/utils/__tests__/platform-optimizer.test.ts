/**
 * Platform Optimizer Tests
 * 平台优化器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    AppOptimizer,
    H5Optimizer,
    MiniProgramOptimizer,
    PlatformOptimizerFactory,
    cleanupPlatformOptimizations,
    getCurrentOptimizer,
    initializePlatformOptimizations
} from '../platform-optimizer';

// Mock uni object
const mockUni = {
    getSystemInfoSync: vi.fn(),
    createIntersectionObserver: vi.fn(),
    getImageInfo: vi.fn()
};

// Mock window and IntersectionObserver
const mockIntersectionObserver = vi.fn();
const mockWindow = {
    IntersectionObserver: mockIntersectionObserver,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
};

describe('MiniProgramOptimizer', () => {
    let optimizer: MiniProgramOptimizer;

    beforeEach(() => {
        // Reset platform detection
        const { PlatformDetector } = require('../platform-adapter');
        (PlatformDetector as any).instance = null;

        // Mock miniprogram environment
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' }),
            createIntersectionObserver: vi.fn(() => ({
                observe: vi.fn(),
                disconnect: vi.fn()
            }))
        };

        optimizer = new MiniProgramOptimizer();
    });

    afterEach(() => {
        optimizer.cleanup();
    });

    it('should initialize optimizations', () => {
        expect(() => optimizer.initializeOptimizations()).not.toThrow();
    });

    it('should optimize scroll performance', () => {
        expect(() => optimizer.optimizeScrollPerformance()).not.toThrow();
    });

    it('should optimize image loading', () => {
        expect(() => optimizer.optimizeImageLoading()).not.toThrow();
    });

    it('should optimize memory usage', () => {
        expect(() => optimizer.optimizeMemoryUsage()).not.toThrow();
    });

    it('should cleanup resources', () => {
        optimizer.initializeOptimizations();
        expect(() => optimizer.cleanup()).not.toThrow();
    });
});

describe('AppOptimizer', () => {
    let optimizer: AppOptimizer;

    beforeEach(() => {
        // Reset platform detection
        const { PlatformDetector } = require('../platform-adapter');
        (PlatformDetector as any).instance = null;

        // Mock app environment
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'android', environment: 'app' })
        };

        optimizer = new AppOptimizer();
    });

    afterEach(() => {
        optimizer.cleanup();
    });

    it('should initialize optimizations', () => {
        expect(() => optimizer.initializeOptimizations()).not.toThrow();
    });

    it('should optimize scroll performance', () => {
        expect(() => optimizer.optimizeScrollPerformance()).not.toThrow();
    });

    it('should optimize image loading', () => {
        expect(() => optimizer.optimizeImageLoading()).not.toThrow();
    });

    it('should optimize memory usage', () => {
        expect(() => optimizer.optimizeMemoryUsage()).not.toThrow();
    });

    it('should cleanup resources', () => {
        optimizer.initializeOptimizations();
        expect(() => optimizer.cleanup()).not.toThrow();
    });
});

describe('H5Optimizer', () => {
    let optimizer: H5Optimizer;
    let mockObserver: any;

    beforeEach(() => {
        // Reset platform detection
        const { PlatformDetector } = require('../platform-adapter');
        (PlatformDetector as any).instance = null;

        // Mock H5 environment
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'h5' })
        };

        mockObserver = {
            observe: vi.fn(),
            unobserve: vi.fn(),
            disconnect: vi.fn()
        };

        (global as any).window = {
            ...mockWindow,
            IntersectionObserver: vi.fn(() => mockObserver)
        };

        optimizer = new H5Optimizer();
    });

    afterEach(() => {
        optimizer.cleanup();
        delete (global as any).window;
    });

    it('should initialize optimizations', () => {
        expect(() => optimizer.initializeOptimizations()).not.toThrow();
    });

    it('should optimize scroll performance', () => {
        expect(() => optimizer.optimizeScrollPerformance()).not.toThrow();
    });

    it('should optimize image loading', () => {
        expect(() => optimizer.optimizeImageLoading()).not.toThrow();
    });

    it('should optimize memory usage', () => {
        expect(() => optimizer.optimizeMemoryUsage()).not.toThrow();
    });

    it('should observe and unobserve elements', () => {
        optimizer.initializeOptimizations();

        const mockElement = document.createElement('div');

        optimizer.observeElement(mockElement);
        expect(mockObserver.observe).toHaveBeenCalledWith(mockElement);

        optimizer.unobserveElement(mockElement);
        expect(mockObserver.unobserve).toHaveBeenCalledWith(mockElement);
    });

    it('should cleanup resources', () => {
        optimizer.initializeOptimizations();
        optimizer.cleanup();

        expect(mockObserver.disconnect).toHaveBeenCalled();
    });
});

describe('PlatformOptimizerFactory', () => {
    beforeEach(() => {
        // Reset platform detection and factory
        const { PlatformDetector } = require('../platform-adapter');
        (PlatformDetector as any).instance = null;
        PlatformOptimizerFactory.clearOptimizers();
    });

    afterEach(() => {
        PlatformOptimizerFactory.clearOptimizers();
    });

    it('should return MiniProgramOptimizer for miniprogram platform', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        const optimizer = PlatformOptimizerFactory.getOptimizer();
        expect(optimizer).toBeInstanceOf(MiniProgramOptimizer);
    });

    it('should return AppOptimizer for app platform', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'android', environment: 'app' })
        };

        const optimizer = PlatformOptimizerFactory.getOptimizer();
        expect(optimizer).toBeInstanceOf(AppOptimizer);
    });

    it('should return H5Optimizer for h5 platform', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'h5' })
        };
        (global as any).window = mockWindow;

        const optimizer = PlatformOptimizerFactory.getOptimizer();
        expect(optimizer).toBeInstanceOf(H5Optimizer);
    });

    it('should return H5Optimizer for unknown platform', () => {
        delete (global as any).uni;
        delete (global as any).window;

        const optimizer = PlatformOptimizerFactory.getOptimizer();
        expect(optimizer).toBeInstanceOf(H5Optimizer);
    });

    it('should cache optimizer instances', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        const optimizer1 = PlatformOptimizerFactory.getOptimizer();
        const optimizer2 = PlatformOptimizerFactory.getOptimizer();

        expect(optimizer1).toBe(optimizer2);
    });

    it('should clear all optimizers', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        const optimizer = PlatformOptimizerFactory.getOptimizer();
        const cleanupSpy = vi.spyOn(optimizer, 'cleanup');

        PlatformOptimizerFactory.clearOptimizers();

        expect(cleanupSpy).toHaveBeenCalled();
    });
});

describe('Convenience functions', () => {
    beforeEach(() => {
        // Reset platform detection and factory
        const { PlatformDetector } = require('../platform-adapter');
        (PlatformDetector as any).instance = null;
        PlatformOptimizerFactory.clearOptimizers();
    });

    afterEach(() => {
        PlatformOptimizerFactory.clearOptimizers();
    });

    it('should get current optimizer', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        const optimizer = getCurrentOptimizer();
        expect(optimizer).toBeInstanceOf(MiniProgramOptimizer);
    });

    it('should initialize platform optimizations', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        expect(() => initializePlatformOptimizations()).not.toThrow();
    });

    it('should cleanup platform optimizations', () => {
        (global as any).uni = {
            ...mockUni,
            getSystemInfoSync: () => ({ platform: 'devtools', environment: 'miniprogram' })
        };

        // Initialize first
        initializePlatformOptimizations();

        // Then cleanup
        expect(() => cleanupPlatformOptimizations()).not.toThrow();
    });
});
