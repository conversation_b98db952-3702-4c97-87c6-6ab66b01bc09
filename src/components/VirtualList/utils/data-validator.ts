/**
 * VirtualList Data Validator
 * 虚拟列表数据验证器
 */

import type { VirtualListItem, VirtualListProps } from '../types';
import { ConfigError, DataError } from '../types';
import { VirtualListErrorHandler } from './error-handler';

export interface ValidationResult {
    isValid: boolean;
    error?: DataError | ConfigError;
    normalizedData?: any[];
    normalizedConfig?: Partial<VirtualListProps>;
}

export class VirtualListDataValidator {
    private errorHandler: VirtualListErrorHandler;

    constructor(errorHandler?: VirtualListErrorHandler) {
        this.errorHandler = errorHandler || new VirtualListErrorHandler();
    }

    /**
     * 验证并规范化数据
     * @param data 原始数据
     * @returns 验证结果
     */
    validateAndNormalizeData(data: any): ValidationResult {
        try {
            // 处理空数据情况
            if (data === null || data === undefined) {
                return {
                    isValid: true,
                    normalizedData: []
                };
            }

            // 处理非数组数据
            if (!Array.isArray(data)) {
                // 尝试转换为数组
                if (typeof data === 'object' && data !== null) {
                    // 如果是对象，尝试获取其值或转换为单项数组
                    const normalizedData = Object.values(data).length > 0 ? Object.values(data) : [data];
                    return {
                        isValid: true,
                        normalizedData: this.normalizeArrayItems(normalizedData)
                    };
                } else {
                    // 基本类型转换为单项数组
                    return {
                        isValid: true,
                        normalizedData: [this.normalizeItem(data, 0)]
                    };
                }
            }

            // 处理空数组
            if (data.length === 0) {
                return {
                    isValid: true,
                    normalizedData: []
                };
            }

            // 处理单项数据
            if (data.length === 1) {
                return {
                    isValid: true,
                    normalizedData: [this.normalizeItem(data[0], 0)]
                };
            }

            // 验证数组项
            const validationResult = this.validateArrayItems(data);
            if (!validationResult.isValid) {
                return validationResult;
            }

            // 规范化数组项
            const normalizedData = this.normalizeArrayItems(data);

            return {
                isValid: true,
                normalizedData
            };
        } catch (error) {
            const dataError = new DataError(
                `Data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
            this.errorHandler.handleDataError(dataError);

            return {
                isValid: false,
                error: dataError,
                normalizedData: [] // 提供空数组作为降级方案
            };
        }
    }

    /**
     * 验证并规范化配置
     * @param config 原始配置
     * @returns 验证结果
     */
    validateAndNormalizeConfig(config: Partial<VirtualListProps>): ValidationResult {
        try {
            const normalizedConfig: Partial<VirtualListProps> = { ...config };
            const errors: string[] = [];

            // 验证并修复高度
            if (!config.height) {
                errors.push('Height is required');
                normalizedConfig.height = '300px'; // 默认高度
            } else if (typeof config.height === 'number' && config.height <= 0) {
                errors.push('Height must be positive');
                normalizedConfig.height = Math.abs(config.height) || 300;
            }

            // 验证并修复项目高度
            if (!config.itemHeight || config.itemHeight <= 0) {
                errors.push('ItemHeight must be positive');
                normalizedConfig.itemHeight = Math.abs(config.itemHeight || 0) || 50;
            }

            // 验证并修复缓冲区大小
            if (config.bufferSize !== undefined) {
                if (config.bufferSize < 0) {
                    errors.push('BufferSize cannot be negative');
                    normalizedConfig.bufferSize = 0;
                } else if (config.bufferSize > 100) {
                    errors.push('BufferSize too large, capping at 100');
                    normalizedConfig.bufferSize = 100;
                }
            }

            // 验证并修复阈值
            if (config.threshold !== undefined) {
                if (config.threshold < 0) {
                    errors.push('Threshold cannot be negative');
                    normalizedConfig.threshold = 0;
                } else if (config.threshold > 1000) {
                    errors.push('Threshold too large, capping at 1000');
                    normalizedConfig.threshold = 1000;
                }
            }

            // 验证并修复滚动位置
            if (config.scrollTop !== undefined && config.scrollTop < 0) {
                errors.push('ScrollTop cannot be negative');
                normalizedConfig.scrollTop = 0;
            }

            // 如果有错误但可以修复，记录警告
            if (errors.length > 0) {
                const configError = new ConfigError(
                    `Configuration issues found and fixed: ${errors.join(', ')}`
                );
                this.errorHandler.handleConfigError(configError);
            }

            return {
                isValid: true,
                normalizedConfig
            };
        } catch (error) {
            const configError = new ConfigError(
                `Config validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
            this.errorHandler.handleConfigError(configError);

            // 提供默认配置作为降级方案
            const defaultConfig = this.errorHandler.getDefaultConfig();

            return {
                isValid: false,
                error: configError,
                normalizedConfig: defaultConfig
            };
        }
    }

    /**
     * 处理边界情况
     * @param data 数据
     * @param config 配置
     * @returns 处理结果
     */
    handleEdgeCases(
        data: any[],
        config: Partial<VirtualListProps>
    ): {
        shouldUseVirtualization: boolean;
        optimizedConfig: Partial<VirtualListProps>;
        warnings: string[];
    } {
        const warnings: string[] = [];
        const optimizedConfig = { ...config };
        let shouldUseVirtualization = true;

        // 处理小数据集
        if (data.length <= 10) {
            shouldUseVirtualization = false;
            warnings.push('Small dataset detected, virtualization disabled for better performance');
        }

        // 处理大数据集
        if (data.length > 10000) {
            // 增加缓冲区以减少频繁渲染
            if (!optimizedConfig.bufferSize || optimizedConfig.bufferSize < 10) {
                optimizedConfig.bufferSize = 10;
                warnings.push('Large dataset detected, buffer size increased');
            }

            // 增加滚动节流
            if (!optimizedConfig.threshold || optimizedConfig.threshold < 50) {
                optimizedConfig.threshold = 50;
                warnings.push('Large dataset detected, scroll threshold increased');
            }
        }

        // 处理极小的项目高度
        if (config.itemHeight && config.itemHeight < 20) {
            warnings.push('Very small item height detected, may affect performance');
        }

        // 处理极大的项目高度
        if (config.itemHeight && config.itemHeight > 500) {
            warnings.push('Very large item height detected, consider reducing buffer size');
            if (!optimizedConfig.bufferSize || optimizedConfig.bufferSize > 3) {
                optimizedConfig.bufferSize = 3;
            }
        }

        // 处理容器高度与项目高度的关系
        if (config.height && config.itemHeight) {
            const containerHeight = typeof config.height === 'number' ? config.height : 300;
            const visibleItems = Math.floor(containerHeight / config.itemHeight);

            if (visibleItems < 2) {
                warnings.push('Container height too small for item height, may cause display issues');
            }

            if (visibleItems > data.length) {
                shouldUseVirtualization = false;
                warnings.push('All items fit in viewport, virtualization disabled');
            }
        }

        return {
            shouldUseVirtualization,
            optimizedConfig,
            warnings
        };
    }

    /**
     * 验证数组项
     * @param data 数据数组
     * @returns 验证结果
     */
    private validateArrayItems(data: any[]): ValidationResult {
        for (let i = 0; i < data.length; i++) {
            const item = data[i];

            // 检查null或undefined项
            if (item === null || item === undefined) {
                const error = new DataError(
                    `Invalid item at index ${i}: item is ${item === null ? 'null' : 'undefined'}`
                );
                this.errorHandler.handleDataError(error);
                return {
                    isValid: false,
                    error
                };
            }

            // 检查循环引用（简单检查）
            if (typeof item === 'object' && this.hasCircularReference(item)) {
                const error = new DataError(`Circular reference detected at index ${i}`);
                this.errorHandler.handleDataError(error);
                return {
                    isValid: false,
                    error
                };
            }
        }

        return { isValid: true };
    }

    /**
     * 规范化数组项
     * @param data 原始数据数组
     * @returns 规范化后的数组
     */
    private normalizeArrayItems(data: any[]): VirtualListItem[] {
        return data.map((item, index) => this.normalizeItem(item, index));
    }

    /**
     * 规范化单个项目
     * @param item 原始项目
     * @param index 索引
     * @returns 规范化后的项目
     */
    private normalizeItem(item: any, index: number): VirtualListItem {
        // 如果已经是规范化的项目，直接返回
        if (item && typeof item === 'object' && ('id' in item || '_virtualIndex' in item)) {
            return {
                ...item,
                _virtualIndex: index,
                _isVisible: false
            };
        }

        // 为基本类型或对象生成规范化项目
        const normalizedItem: VirtualListItem = {
            id: this.generateItemId(item, index),
            _virtualIndex: index,
            _isVisible: false
        };

        // 如果是对象，合并属性
        if (typeof item === 'object' && item !== null) {
            Object.assign(normalizedItem, item);
        } else {
            // 如果是基本类型，作为value属性
            normalizedItem.value = item;
        }

        return normalizedItem;
    }

    /**
     * 生成项目ID
     * @param item 项目
     * @param index 索引
     * @returns 生成的ID
     */
    private generateItemId(item: any, index: number): string | number {
        // 如果项目已有id，使用它
        if (item && typeof item === 'object' && 'id' in item) {
            return item.id;
        }

        // 如果项目有唯一标识符，使用它
        if (item && typeof item === 'object') {
            const uniqueKeys = ['key', 'uuid', 'guid', '_id'];
            for (const key of uniqueKeys) {
                if (key in item && (typeof item[key] === 'string' || typeof item[key] === 'number')) {
                    return item[key];
                }
            }
        }

        // 否则使用索引
        return `virtual-item-${index}`;
    }

    /**
     * 检查循环引用（简单实现）
     * @param obj 对象
     * @param seen 已见过的对象集合
     * @returns 是否有循环引用
     */
    private hasCircularReference(obj: any, seen = new WeakSet()): boolean {
        if (typeof obj !== 'object' || obj === null) {
            return false;
        }

        if (seen.has(obj)) {
            return true;
        }

        seen.add(obj);

        try {
            for (const key in obj) {
                if (obj.hasOwnProperty(key) && this.hasCircularReference(obj[key], seen)) {
                    return true;
                }
            }
        } catch {
            // 如果访问属性时出错，假设没有循环引用
            return false;
        }

        seen.delete(obj);
        return false;
    }

    /**
     * 获取数据统计信息
     * @param data 数据数组
     * @returns 统计信息
     */
    getDataStats(data: any[]): {
        totalItems: number;
        hasNullItems: boolean;
        hasUndefinedItems: boolean;
        hasObjectItems: boolean;
        hasPrimitiveItems: boolean;
        averageItemSize: number;
    } {
        if (!Array.isArray(data) || data.length === 0) {
            return {
                totalItems: 0,
                hasNullItems: false,
                hasUndefinedItems: false,
                hasObjectItems: false,
                hasPrimitiveItems: false,
                averageItemSize: 0
            };
        }

        let nullCount = 0;
        let undefinedCount = 0;
        let objectCount = 0;
        let primitiveCount = 0;
        let totalSize = 0;

        data.forEach((item) => {
            if (item === null) {
                nullCount++;
            } else if (item === undefined) {
                undefinedCount++;
            } else if (typeof item === 'object') {
                objectCount++;
                totalSize += this.estimateObjectSize(item);
            } else {
                primitiveCount++;
                totalSize += this.estimatePrimitiveSize(item);
            }
        });

        return {
            totalItems: data.length,
            hasNullItems: nullCount > 0,
            hasUndefinedItems: undefinedCount > 0,
            hasObjectItems: objectCount > 0,
            hasPrimitiveItems: primitiveCount > 0,
            averageItemSize: data.length > 0 ? totalSize / data.length : 0
        };
    }

    /**
     * 估算对象大小（字节）
     * @param obj 对象
     * @returns 估算大小
     */
    private estimateObjectSize(obj: any): number {
        if (obj === null || obj === undefined) return 0;

        try {
            return JSON.stringify(obj).length * 2; // 粗略估算，每字符2字节
        } catch {
            return 100; // 默认估算值
        }
    }

    /**
     * 估算基本类型大小（字节）
     * @param value 值
     * @returns 估算大小
     */
    private estimatePrimitiveSize(value: any): number {
        if (typeof value === 'string') {
            return value.length * 2;
        } else if (typeof value === 'number') {
            return 8;
        } else if (typeof value === 'boolean') {
            return 4;
        }
        return 4;
    }
}

// 创建默认实例
export const virtualListDataValidator = new VirtualListDataValidator();
