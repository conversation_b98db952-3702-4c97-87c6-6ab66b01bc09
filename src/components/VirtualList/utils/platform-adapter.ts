/**
 * Platform Adapter for VirtualList Component
 * 虚拟列表组件平台适配器
 */

import type { PlatformConfig } from '../types';

// 平台类型定义
export type PlatformType = 'mp-weixin' | 'app' | 'h5' | 'unknown';

// 平台特定配置接口
export interface PlatformSpecificConfig {
  // 滚动相关配置
  useNativeScroll: boolean;
  enableVirtualization: boolean;
  scrollEventThrottle: number;

  // 性能优化配置
  optimizeImages: boolean;
  enablePassiveScroll: boolean;
  useIntersectionObserver: boolean;

  // 平台特定属性
  enhanced?: boolean;
  bounces?: boolean;
  showScrollbar?: boolean;
  enableBackToTop?: boolean;

  // 渲染优化
  useTransform: boolean;
  enableHardwareAcceleration: boolean;
  bufferSizeMultiplier: number;
}

/**
 * 平台检测器
 */
export class PlatformDetector {
  private static instance: PlatformDetector;
  private _platform: PlatformType | null = null;

  private constructor() { }

  static getInstance(): PlatformDetector {
    if (!PlatformDetector.instance) {
      PlatformDetector.instance = new PlatformDetector();
    }
    return PlatformDetector.instance;
  }

  /**
   * 检测当前运行平台
   */
  detectPlatform(): PlatformType {
    if (this._platform) {
      return this._platform;
    }

    // 检测uni-app环境
    if (typeof uni !== 'undefined') {
      const systemInfo = uni.getSystemInfoSync();

      // 检测小程序环境
      if (
        systemInfo.platform === 'devtools' ||
        (systemInfo as any).environment === 'miniprogram' ||
        /miniProgram/i.test((systemInfo as any).environment || '')
      ) {
        this._platform = 'mp-weixin';
      }
      // 检测App环境
      else if (
        systemInfo.platform === 'android' ||
        systemInfo.platform === 'ios' ||
        /app/i.test((systemInfo as any).environment || '')
      ) {
        this._platform = 'app';
      }
      // 检测H5环境
      else if (systemInfo.platform === 'h5' || typeof window !== 'undefined') {
        this._platform = 'h5';
      } else {
        this._platform = 'unknown';
      }
    }
    // 纯H5环境
    else if (typeof window !== 'undefined') {
      this._platform = 'h5';
    } else {
      this._platform = 'unknown';
    }

    return this._platform;
  }

  /**
   * 获取当前平台
   */
  getPlatform(): PlatformType {
    return this._platform || this.detectPlatform();
  }

  /**
   * 检查是否为小程序环境
   */
  isMiniProgram(): boolean {
    return this.getPlatform() === 'mp-weixin';
  }

  /**
   * 检查是否为App环境
   */
  isApp(): boolean {
    return this.getPlatform() === 'app';
  }

  /**
   * 检查是否为H5环境
   */
  isH5(): boolean {
    return this.getPlatform() === 'h5';
  }

  /**
   * 检查是否支持Intersection Observer API
   */
  supportsIntersectionObserver(): boolean {
    if (this.isH5()) {
      return typeof window !== 'undefined' && 'IntersectionObserver' in window;
    }
    if (this.isMiniProgram()) {
      return typeof uni !== 'undefined' && typeof uni.createIntersectionObserver === 'function';
    }
    return false;
  }

  /**
   * 检查是否支持被动滚动事件
   */
  supportsPassiveEvents(): boolean {
    if (this.isH5()) {
      let supportsPassive = false;
      try {
        const opts = Object.defineProperty({}, 'passive', {
          get() {
            supportsPassive = true;
            return false;
          }
        });
        window.addEventListener('testPassive', () => { }, opts);
        window.removeEventListener('testPassive', () => { }, opts);
      } catch (e) {
        // ignore
      }
      return supportsPassive;
    }
    return this.isMiniProgram() || this.isApp();
  }
}

/**
 * 平台配置管理器
 */
export class PlatformConfigManager {
  private static instance: PlatformConfigManager;
  private detector: PlatformDetector;
  private configs: Map<PlatformType, PlatformSpecificConfig>;

  private constructor() {
    this.detector = PlatformDetector.getInstance();
    this.configs = new Map();
    this.initializeConfigs();
  }

  static getInstance(): PlatformConfigManager {
    if (!PlatformConfigManager.instance) {
      PlatformConfigManager.instance = new PlatformConfigManager();
    }
    return PlatformConfigManager.instance;
  }

  /**
   * 初始化各平台配置
   */
  private initializeConfigs(): void {
    // 小程序配置
    this.configs.set('mp-weixin', {
      useNativeScroll: true,
      enableVirtualization: true,
      scrollEventThrottle: 50, // 增加节流时间，减少抖动
      optimizeImages: true,
      enablePassiveScroll: true,
      useIntersectionObserver: true,
      enhanced: true,
      bounces: false, // 小程序端关闭回弹，减少抖动
      showScrollbar: false,
      enableBackToTop: true,
      useTransform: true,
      enableHardwareAcceleration: true,
      bufferSizeMultiplier: 2.0 // 增加缓冲区，减少空白
    });

    // App端配置
    this.configs.set('app', {
      useNativeScroll: true,
      enableVirtualization: true,
      scrollEventThrottle: 8,
      optimizeImages: true,
      enablePassiveScroll: true,
      useIntersectionObserver: false,
      bounces: true,
      showScrollbar: false,
      enableBackToTop: true,
      useTransform: true,
      enableHardwareAcceleration: true,
      bufferSizeMultiplier: 2.0
    });

    // H5配置
    this.configs.set('h5', {
      useNativeScroll: true,
      enableVirtualization: true,
      scrollEventThrottle: 16,
      optimizeImages: true,
      enablePassiveScroll: this.detector.supportsPassiveEvents(),
      useIntersectionObserver: this.detector.supportsIntersectionObserver(),
      showScrollbar: true,
      enableBackToTop: true,
      useTransform: true,
      enableHardwareAcceleration: true,
      bufferSizeMultiplier: 1.0
    });

    // 未知平台默认配置
    this.configs.set('unknown', {
      useNativeScroll: false,
      enableVirtualization: false,
      scrollEventThrottle: 32,
      optimizeImages: false,
      enablePassiveScroll: false,
      useIntersectionObserver: false,
      showScrollbar: true,
      enableBackToTop: false,
      useTransform: false,
      enableHardwareAcceleration: false,
      bufferSizeMultiplier: 1.0
    });
  }

  /**
   * 获取当前平台配置
   */
  getCurrentConfig(): PlatformSpecificConfig {
    const platform = this.detector.getPlatform();
    return this.configs.get(platform) || this.configs.get('unknown')!;
  }

  /**
   * 获取指定平台配置
   */
  getConfigForPlatform(platform: PlatformType): PlatformSpecificConfig {
    return this.configs.get(platform) || this.configs.get('unknown')!;
  }

  /**
   * 更新平台配置
   */
  updateConfig(platform: PlatformType, config: Partial<PlatformSpecificConfig>): void {
    const currentConfig = this.configs.get(platform) || this.configs.get('unknown')!;
    this.configs.set(platform, { ...currentConfig, ...config });
  }

  /**
   * 生成VirtualList组件的PlatformConfig
   */
  generatePlatformConfig(): PlatformConfig {
    const platform = this.detector.getPlatform();
    const config = this.getCurrentConfig();

    return {
      platform: platform === 'unknown' ? 'h5' : platform,
      useNativeScroll: config.useNativeScroll,
      enableVirtualization: config.enableVirtualization,
      optimizeImages: config.optimizeImages,
      scrollEventThrottle: config.scrollEventThrottle
    };
  }
}

/**
 * 平台特定滚动实现
 */
export class PlatformScrollImplementation {
  private detector: PlatformDetector;
  private config: PlatformSpecificConfig;

  constructor() {
    this.detector = PlatformDetector.getInstance();
    this.config = PlatformConfigManager.getInstance().getCurrentConfig();
  }

  /**
   * 获取滚动容器的属性配置
   */
  getScrollViewProps(): Record<string, any> {
    const platform = this.detector.getPlatform();
    const baseProps: Record<string, any> = {
      'scroll-y': true,
      'enable-flex': true,
      'scroll-with-animation': false
    };

    switch (platform) {
      case 'mp-weixin':
        return {
          ...baseProps,
          enhanced: this.config.enhanced,
          bounces: this.config.bounces,
          'show-scrollbar': this.config.showScrollbar,
          'enable-passive': this.config.enablePassiveScroll,
          'enable-back-to-top': this.config.enableBackToTop,
          throttle: this.config.scrollEventThrottle
        };

      case 'app':
        return {
          ...baseProps,
          bounces: this.config.bounces,
          'show-scrollbar': this.config.showScrollbar,
          'enable-back-to-top': this.config.enableBackToTop
        };

      case 'h5':
        return {
          ...baseProps,
          'show-scrollbar': this.config.showScrollbar
        };

      default:
        return baseProps;
    }
  }

  /**
   * 获取滚动事件监听器配置
   */
  getScrollEventConfig(): {
    eventName: string;
    options?: AddEventListenerOptions;
    throttle: number;
  } {
    return {
      eventName: 'scroll',
      options: this.config.enablePassiveScroll ? { passive: true } : undefined,
      throttle: this.config.scrollEventThrottle
    };
  }

  /**
   * 获取平台特定的CSS样式
   */
  getPlatformStyles(): Record<string, string> {
    const styles: Record<string, string> = {};

    if (this.config.enableHardwareAcceleration) {
      styles.transform = 'translateZ(0)';
      styles.willChange = 'transform';
    }

    if (this.config.useTransform) {
      styles.backfaceVisibility = 'hidden';
      styles.perspective = '1000px';
    }

    // 平台特定样式
    const platform = this.detector.getPlatform();
    switch (platform) {
      case 'mp-weixin':
        styles.webkitOverflowScrolling = 'touch';
        break;
      case 'app':
        styles.webkitOverflowScrolling = 'touch';
        styles.overflowScrolling = 'touch';
        break;
      case 'h5':
        if (!this.config.showScrollbar) {
          styles.scrollbarWidth = 'none';
          styles.msOverflowStyle = 'none';
        }
        break;
    }

    return styles;
  }

  /**
   * 创建平台特定的Intersection Observer
   */
  createIntersectionObserver(callback: (entries: any[]) => void): any {
    const platform = this.detector.getPlatform();

    if (platform === 'mp-weixin' && typeof uni !== 'undefined') {
      return uni.createIntersectionObserver(null, {
        thresholds: [0, 0.25, 0.5, 0.75, 1],
        initialRatio: 0,
        observeAll: true
      });
    }

    if (platform === 'h5' && this.detector.supportsIntersectionObserver()) {
      return new IntersectionObserver(callback, {
        root: null,
        rootMargin: '50px',
        threshold: [0, 0.25, 0.5, 0.75, 1]
      });
    }

    return null;
  }
}

// 导出单例实例
export const platformDetector = PlatformDetector.getInstance();
export const platformConfigManager = PlatformConfigManager.getInstance();
export const platformScrollImplementation = new PlatformScrollImplementation();

// 便捷函数
export function getCurrentPlatform(): PlatformType {
  return platformDetector.getPlatform();
}

export function getCurrentPlatformConfig(): PlatformSpecificConfig {
  return platformConfigManager.getCurrentConfig();
}

export function isMiniProgram(): boolean {
  return platformDetector.isMiniProgram();
}

export function isApp(): boolean {
  return platformDetector.isApp();
}

export function isH5(): boolean {
  return platformDetector.isH5();
}
