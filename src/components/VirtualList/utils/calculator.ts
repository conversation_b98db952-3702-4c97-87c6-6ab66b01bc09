/**
 * 可视区域计算器
 * Viewport Calculator for Virtual List
 */

import type { BufferedRange, IViewportCalculator, ItemPosition, VisibleRange } from '../types';

export class ViewportCalculator implements IViewportCalculator {
  private containerHeight = 0;
  private itemHeight = 0;
  private scrollTop = 0;

  constructor(containerHeight: number, itemHeight: number) {
    this.containerHeight = containerHeight;
    this.itemHeight = itemHeight;
  }

  /**
   * 更新容器高度
   */
  updateContainerHeight(height: number): void {
    this.containerHeight = height;
  }

  /**
   * 更新列表项高度
   */
  updateItemHeight(height: number): void {
    this.itemHeight = height;
  }

  /**
   * 更新滚动位置
   */
  updateScrollTop(scrollTop: number): void {
    this.scrollTop = scrollTop;
  }

  /**
   * 获取可视区域范围
   * 计算当前滚动位置下可见的列表项索引范围
   */
  getVisibleRange(): VisibleRange {
    if (this.itemHeight <= 0 || this.containerHeight <= 0) {
      return { start: 0, end: 0 };
    }

    // 计算可视区域开始索引，确保不为负数
    const start = Math.max(0, Math.floor(this.scrollTop / this.itemHeight));

    // 计算可视区域结束索引，添加额外的缓冲项以确保完整覆盖
    const visibleItemCount = Math.ceil(this.containerHeight / this.itemHeight);
    const end = start + visibleItemCount + 1; // 添加1个额外项，确保完整覆盖

    return {
      start,
      end: Math.max(start + 1, end) // 确保至少有一个项目
    };
  }

  /**
   * 获取缓冲区范围
   * 在可视区域基础上扩展缓冲区，减少滚动时的白屏现象
   */
  getBufferedRange(bufferSize: number): BufferedRange {
    const visibleRange = this.getVisibleRange();
    const safeBufferSize = Math.max(0, bufferSize || 5); // 确保缓冲区大小有效

    const start = Math.max(0, visibleRange.start - safeBufferSize);
    const end = visibleRange.end + safeBufferSize;

    return {
      start,
      end: Math.max(start + 1, end) // 确保至少有一个项目
    };
  }

  /**
   * 计算总高度
   * 根据列表项数量和单项高度计算列表总高度
   */
  calculateTotalHeight(itemCount: number): number {
    return Math.max(0, itemCount * this.itemHeight);
  }

  /**
   * 获取列表项位置信息
   * 计算指定索引列表项的位置和尺寸信息
   */
  getItemPosition(index: number): ItemPosition {
    return {
      top: Math.max(0, index * this.itemHeight),
      height: this.itemHeight
    };
  }

  /**
   * 获取当前滚动位置
   */
  getScrollTop(): number {
    return this.scrollTop;
  }

  /**
   * 获取容器高度
   */
  getContainerHeight(): number {
    return this.containerHeight;
  }

  /**
   * 获取列表项高度
   */
  getItemHeight(): number {
    return this.itemHeight;
  }

  /**
   * 计算滚动到指定索引所需的滚动位置
   */
  getScrollTopForIndex(index: number): number {
    return Math.max(0, index * this.itemHeight);
  }

  /**
   * 检查指定索引是否在可视区域内
   */
  isIndexVisible(index: number): boolean {
    const range = this.getVisibleRange();
    return index >= range.start && index < range.end;
  }

  /**
   * 检查指定索引是否在缓冲区内
   */
  isIndexInBuffer(index: number, bufferSize: number): boolean {
    const range = this.getBufferedRange(bufferSize);
    return index >= range.start && index < range.end;
  }
}
