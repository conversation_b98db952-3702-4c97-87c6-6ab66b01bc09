/**
 * 滚动管理器
 * Scroll Manager for Virtual List
 */

import type { IScrollManager, ScrollEvent } from '../types';
import type { ViewportCalculator } from './calculator';
import { requestAnimationFrame } from './raf-polyfill';

export class ScrollManager implements IScrollManager {
  private scrollTop = 0;
  private isScrolling = false;
  private scrollTimer: number | null = null;
  private calculator: ViewportCalculator | null = null;
  private scrollToCallback: ((scrollTop: number) => void) | null = null;
  private eventCallbacks: {
    onScroll?: (event: ScrollEvent) => void;
    onScrollToTop?: () => void;
    onScrollToBottom?: () => void;
  } = {};

  /**
   * 设置计算器实例
   */
  setCalculator(calculator: ViewportCalculator): void {
    this.calculator = calculator;
  }

  /**
   * 设置滚动回调函数
   */
  setScrollToCallback(callback: (scrollTop: number) => void): void {
    this.scrollToCallback = callback;
  }

  /**
   * 设置事件回调函数
   */
  setEventCallbacks(callbacks: {
    onScroll?: (event: ScrollEvent) => void;
    onScrollToTop?: () => void;
    onScrollToBottom?: () => void;
  }): void {
    this.eventCallbacks = callbacks;
  }

  /**
   * 处理滚动事件
   */
  handleScroll(event: ScrollEvent): void {
    const newScrollTop = event.detail.scrollTop;
    const scrollHeight = event.detail.scrollHeight;
    const containerHeight = this.calculator?.getContainerHeight() || 0;

    // 更新滚动位置
    this.scrollTop = newScrollTop;
    this.isScrolling = true;

    // 更新计算器的滚动位置
    if (this.calculator) {
      this.calculator.updateScrollTop(newScrollTop);
    }

    // 触发滚动事件回调
    if (this.eventCallbacks.onScroll) {
      this.eventCallbacks.onScroll(event);
    }

    // 检查是否滚动到顶部
    if (newScrollTop <= 0 && this.eventCallbacks.onScrollToTop) {
      this.eventCallbacks.onScrollToTop();
    }

    // 检查是否滚动到底部
    if (
      containerHeight > 0 &&
      scrollHeight > 0 &&
      newScrollTop + containerHeight >= scrollHeight - 10 && // 10px tolerance
      this.eventCallbacks.onScrollToBottom
    ) {
      this.eventCallbacks.onScrollToBottom();
    }

    // 清除现有定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    // 设置滚动结束标志
    this.scrollTimer = setTimeout(() => {
      this.isScrolling = false;
    }, 150) as unknown as number;
  }

  /**
   * 滚动到指定索引
   */
  scrollToIndex(index: number): void {
    if (!this.calculator) {
      console.warn('ScrollManager: Calculator not set, cannot scroll to index');
      return;
    }

    if (index < 0) {
      console.warn('ScrollManager: Invalid index, cannot be negative');
      return;
    }

    const scrollTop = this.calculator.getScrollTopForIndex(index);
    this.scrollToPosition(scrollTop);
  }

  /**
   * 滚动到顶部
   */
  scrollToTop(): void {
    this.scrollToPosition(0);
  }

  /**
   * 滚动到底部
   */
  scrollToBottom(): void {
    if (!this.calculator) {
      console.warn('ScrollManager: Calculator not set, cannot scroll to bottom');
      return;
    }

    // 计算最大滚动位置
    const containerHeight = this.calculator.getContainerHeight();
    const totalHeight = this.calculator.calculateTotalHeight(Number.MAX_SAFE_INTEGER);
    const maxScrollTop = Math.max(0, totalHeight - containerHeight);

    this.scrollToPosition(maxScrollTop);
  }

  /**
   * 滚动到指定位置
   */
  private scrollToPosition(scrollTop: number): void {
    if (this.scrollToCallback) {
      this.scrollToCallback(scrollTop);
    } else {
      console.warn('ScrollManager: Scroll callback not set, cannot scroll to position');
    }
  }

  /**
   * 获取当前滚动位置
   */
  getScrollTop(): number {
    return this.scrollTop;
  }

  /**
   * 获取滚动状态
   */
  getIsScrolling(): boolean {
    return this.isScrolling;
  }

  /**
   * 设置滚动位置（不触发滚动动画）
   */
  setScrollTop(scrollTop: number): void {
    this.scrollTop = Math.max(0, scrollTop);
    if (this.calculator) {
      this.calculator.updateScrollTop(this.scrollTop);
    }
  }

  /**
   * 获取滚动进度（0-1）
   */
  getScrollProgress(): number {
    if (!this.calculator) {
      return 0;
    }

    const containerHeight = this.calculator.getContainerHeight();
    const totalHeight = this.calculator.calculateTotalHeight(Number.MAX_SAFE_INTEGER);
    const maxScrollTop = Math.max(0, totalHeight - containerHeight);

    if (maxScrollTop <= 0) {
      return 0;
    }

    return Math.min(1, Math.max(0, this.scrollTop / maxScrollTop));
  }

  /**
   * 检查是否可以向上滚动
   */
  canScrollUp(): boolean {
    return this.scrollTop > 0;
  }

  /**
   * 检查是否可以向下滚动
   */
  canScrollDown(): boolean {
    if (!this.calculator) {
      return false;
    }

    const containerHeight = this.calculator.getContainerHeight();
    const totalHeight = this.calculator.calculateTotalHeight(Number.MAX_SAFE_INTEGER);
    const maxScrollTop = Math.max(0, totalHeight - containerHeight);

    return this.scrollTop < maxScrollTop;
  }

  /**
   * 平滑滚动到指定位置
   */
  smoothScrollTo(targetScrollTop: number, duration = 300): void {
    const startScrollTop = this.scrollTop;
    const distance = targetScrollTop - startScrollTop;
    const startTime = Date.now();

    const animateScroll = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeProgress = this.easeInOutCubic(progress);
      const currentScrollTop = startScrollTop + distance * easeProgress;

      this.scrollToPosition(currentScrollTop);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }

  /**
   * 缓动函数
   */
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - (-2 * t + 2) ** 3 / 2;
  }

  /**
   * 清理定时器和资源
   */
  destroy(): void {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
    this.calculator = null;
    this.scrollToCallback = null;
    this.eventCallbacks = {};
  }
}
