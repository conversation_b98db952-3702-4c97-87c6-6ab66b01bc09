/**
 * requestAnimationFrame 跨平台兼容性处理
 * Cross-platform requestAnimationFrame polyfill
 */

// 跨平台的requestAnimationFrame实现
export const requestAnimationFrame = (() => {
  // H5环境
  if (typeof window !== 'undefined' && window.requestAnimationFrame) {
    return window.requestAnimationFrame.bind(window);
  }

  // 小程序和App环境的polyfill
  return (callback: FrameRequestCallback) => {
    return setTimeout(() => {
      callback(Date.now());
    }, 16); // 约60fps
  };
})();

// 跨平台的cancelAnimationFrame实现
export const cancelAnimationFrame = (() => {
  // H5环境
  if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
    return window.cancelAnimationFrame.bind(window);
  }

  // 小程序和App环境的polyfill
  return (id: number) => {
    clearTimeout(id);
  };
})();

// 跨平台的performance.now实现
export const performanceNow = (() => {
  // H5环境
  if (typeof performance !== 'undefined' && performance.now) {
    return performance.now.bind(performance);
  }

  // 小程序和App环境的polyfill
  return () => Date.now();
})();

// 节流函数，用于性能优化
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  let timeoutId: number | null = null;

  return (...args: Parameters<T>) => {
    const now = Date.now();

    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(
        () => {
          lastCall = Date.now();
          func(...args);
        },
        delay - (now - lastCall)
      ) as unknown as number;
    }
  };
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay) as unknown as number;
  };
}
