/**
 * VirtualList Error Handler
 * 虚拟列表错误处理器
 */

import type {
  IVirtualListErrorHandler,
  PerformanceError,
  PlatformError,
  VirtualListProps
} from '../types';
import { ConfigError, DataError } from '../types';

export class VirtualListErrorHandler implements IVirtualListErrorHandler {
  private fallbackMode = false;
  private errorCount = 0;
  private maxErrors = 10;

  /**
   * 处理数据错误
   * @param error 数据错误
   */
  handleDataError(error: DataError): void {
    console.warn('VirtualList: Data error detected', {
      message: error.message,
      type: error.type,
      timestamp: new Date().toISOString()
    });

    this.errorCount++;

    // 如果错误过多，启用降级模式
    if (this.errorCount > this.maxErrors) {
      this.enableFallbackMode();
    }

    // 触发用户友好的错误提示
    this.showUserFriendlyError('数据加载异常，请检查数据格式');
  }

  /**
   * 处理配置错误
   * @param error 配置错误
   */
  handleConfigError(error: ConfigError): void {
    console.warn('VirtualList: Configuration error detected', {
      message: error.message,
      type: error.type,
      timestamp: new Date().toISOString()
    });

    this.errorCount++;

    // 配置错误通常可以通过默认值修复
    this.applyDefaultConfig();

    // 显示警告信息
    this.showUserFriendlyError('配置参数异常，已使用默认配置');
  }

  /**
   * 处理平台兼容性错误
   * @param error 平台错误
   */
  handlePlatformError(error: PlatformError): void {
    console.warn('VirtualList: Platform compatibility error detected', {
      message: error.message,
      type: error.type,
      platform: this.getCurrentPlatform(),
      timestamp: new Date().toISOString()
    });

    // 平台错误需要启用降级模式
    this.enableFallbackMode();

    // 显示平台兼容性提示
    this.showUserFriendlyError('当前平台兼容性问题，已启用兼容模式');
  }

  /**
   * 处理性能错误
   * @param error 性能错误
   */
  handlePerformanceError(error: PerformanceError): void {
    console.error('VirtualList: Performance issue detected', {
      message: error.message,
      type: error.type,
      timestamp: new Date().toISOString(),
      memoryUsage: this.getMemoryUsage()
    });

    // 性能错误需要立即采取措施
    this.optimizePerformance();

    // 显示性能优化提示
    this.showUserFriendlyError('检测到性能问题，已自动优化');
  }

  /**
   * 验证数据格式
   * @param data 数据数组
   * @returns 验证结果
   */
  validateData(data: any[]): { isValid: boolean; error?: DataError } {
    try {
      // 检查数据是否为数组
      if (!Array.isArray(data)) {
        return {
          isValid: false,
          error: new DataError('Data must be an array')
        };
      }

      // 检查数组是否为空
      if (data.length === 0) {
        return {
          isValid: false,
          error: new DataError('Data array is empty')
        };
      }

      // 检查数组项是否有效
      for (let i = 0; i < Math.min(data.length, 10); i++) {
        const item = data[i];
        if (item === null || item === undefined) {
          return {
            isValid: false,
            error: new DataError(`Invalid item at index ${i}: item is null or undefined`)
          };
        }
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: new DataError(
          `Data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      };
    }
  }

  /**
   * 验证配置参数
   * @param props 组件属性
   * @returns 验证结果
   */
  validateConfig(props: Partial<VirtualListProps>): { isValid: boolean; error?: ConfigError } {
    try {
      // 检查必需的高度参数
      if (!props.height || (typeof props.height === 'number' && props.height <= 0)) {
        return {
          isValid: false,
          error: new ConfigError('Height must be a positive number or valid CSS value')
        };
      }

      // 检查项目高度
      if (!props.itemHeight || props.itemHeight <= 0) {
        return {
          isValid: false,
          error: new ConfigError('ItemHeight must be a positive number')
        };
      }

      // 检查缓冲区大小
      if (props.bufferSize !== undefined && (props.bufferSize < 0 || props.bufferSize > 100)) {
        return {
          isValid: false,
          error: new ConfigError('BufferSize must be between 0 and 100')
        };
      }

      // 检查阈值
      if (props.threshold !== undefined && (props.threshold < 0 || props.threshold > 1000)) {
        return {
          isValid: false,
          error: new ConfigError('Threshold must be between 0 and 1000')
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: new ConfigError(
          `Config validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      };
    }
  }

  /**
   * 获取默认配置
   * @returns 默认配置对象
   */
  getDefaultConfig(): Partial<VirtualListProps> {
    return {
      height: '300px',
      itemHeight: 50,
      bufferSize: 5,
      threshold: 100,
      dynamicHeight: false,
      enableBackToTop: true,
      enhanced: true,
      bounces: true,
      showScrollbar: true
    };
  }

  /**
   * 启用降级模式
   */
  private enableFallbackMode(): void {
    if (!this.fallbackMode) {
      this.fallbackMode = true;
      console.warn('VirtualList: Fallback mode enabled due to multiple errors');

      // 在降级模式下禁用虚拟化
      this.disableVirtualization();
    }
  }

  /**
   * 应用默认配置
   */
  private applyDefaultConfig(): void {
    const defaultConfig = this.getDefaultConfig();
    console.info('VirtualList: Applying default configuration', defaultConfig);
  }

  /**
   * 优化性能
   */
  private optimizePerformance(): void {
    // 减少缓冲区大小
    console.info('VirtualList: Reducing buffer size for performance optimization');

    // 启用节流
    console.info('VirtualList: Enabling scroll throttling');

    // 清理不必要的监听器
    this.cleanupEventListeners();
  }

  /**
   * 禁用虚拟化
   */
  private disableVirtualization(): void {
    console.warn('VirtualList: Virtualization disabled, falling back to regular list');
  }

  /**
   * 清理事件监听器
   */
  private cleanupEventListeners(): void {
    console.info('VirtualList: Cleaning up event listeners for performance');
  }

  /**
   * 显示用户友好的错误提示
   * @param message 错误消息
   */
  private showUserFriendlyError(message: string): void {
    // 在实际应用中，这里可以集成 uni.showToast 或其他提示组件
    console.info('VirtualList User Message:', message);

    // 如果在 uni-app 环境中，可以使用 uni.showToast
    if (typeof uni !== 'undefined' && uni.showToast) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    }
  }

  /**
   * 获取当前平台
   * @returns 平台标识
   */
  private getCurrentPlatform(): string {
    // #ifdef MP-WEIXIN
    return 'mp-weixin';
    // #endif

    // #ifdef APP-PLUS
    return 'app';
    // #endif

    // #ifdef H5
    return 'h5';
    // #endif

    return 'unknown';
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用信息
   */
  private getMemoryUsage(): any {
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * 重置错误计数
   */
  resetErrorCount(): void {
    this.errorCount = 0;
    this.fallbackMode = false;
  }

  /**
   * 获取错误统计
   * @returns 错误统计信息
   */
  getErrorStats(): { errorCount: number; fallbackMode: boolean } {
    return {
      errorCount: this.errorCount,
      fallbackMode: this.fallbackMode
    };
  }

  /**
   * 设置最大错误数
   * @param maxErrors 最大错误数
   */
  setMaxErrors(maxErrors: number): void {
    this.maxErrors = Math.max(1, maxErrors);
  }
}

// 创建单例实例
export const virtualListErrorHandler = new VirtualListErrorHandler();
