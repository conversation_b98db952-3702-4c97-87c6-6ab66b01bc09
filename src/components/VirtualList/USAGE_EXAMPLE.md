# 虚拟列表使用示例

## 基础使用

### 1. 简单列表

```vue
<template>
  <view class="container">
    <VirtualList
      :data="listData"
      :height="400"
      :item-height="60"
      :buffer-size="8"
      @scroll="onScroll"
      @scrollToBottom="loadMore"
    >
      <template #default="{ item, index }">
        <view class="list-item">
          <text>{{ index }}: {{ item.title }}</text>
        </view>
      </template>
    </VirtualList>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VirtualList from '@/components/VirtualList/index.vue';

const listData = ref([
  { id: 1, title: '项目1' },
  { id: 2, title: '项目2' },
  // ... 更多数据
]);

const onScroll = (event) => {
  console.log('滚动位置:', event.detail.scrollTop);
};

const loadMore = () => {
  // 加载更多数据
  const newData = generateMoreData();
  listData.value.push(...newData);
};
</script>
```

### 2. 微信小程序优化配置

```vue
<template>
  <VirtualList
    :data="listData"
    :height="400"
    :item-height="60"
    :buffer-size="10"
    :enhanced="true"
    :bounces="false"
    :show-scrollbar="true"
    @scroll="onScroll"
    @scrollToBottom="loadMore"
  >
    <template #default="{ item, index }">
      <view class="mp-list-item">
        <view class="item-content">
          <text class="item-title">{{ item.title }}</text>
          <text class="item-desc">{{ item.description }}</text>
        </view>
      </view>
    </template>
  </VirtualList>
</template>

<style scoped>
.mp-list-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-title {
  font-size: 16px;
  color: #333;
}

.item-desc {
  font-size: 14px;
  color: #666;
}
</style>
```

## 高级功能

### 1. 滚动控制

```vue
<script setup lang="ts">
import { ref } from 'vue';

const virtualListRef = ref();

// 滚动到指定索引
const scrollToIndex = (index: number) => {
  virtualListRef.value?.scrollToIndex(index);
};

// 滚动到顶部
const scrollToTop = () => {
  virtualListRef.value?.scrollToTop();
};

// 滚动到底部
const scrollToBottom = () => {
  virtualListRef.value?.scrollToBottom();
};
</script>

<template>
  <view>
    <view class="controls">
      <button @click="scrollToTop">回到顶部</button>
      <button @click="scrollToBottom">滚动到底部</button>
      <button @click="scrollToIndex(50)">滚动到第50项</button>
    </view>
    
    <VirtualList
      ref="virtualListRef"
      :data="listData"
      :height="400"
      :item-height="60"
    >
      <template #default="{ item, index }">
        <view class="list-item">
          {{ index }}: {{ item.title }}
        </view>
      </template>
    </VirtualList>
  </view>
</template>
```

### 2. 动态数据更新

```vue
<script setup lang="ts">
import { ref, watch } from 'vue';

const listData = ref([]);
const loading = ref(false);

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const data = await fetchDataFromAPI();
    listData.value = data;
  } finally {
    loading.value = false;
  }
};

// 加载更多
const loadMore = async () => {
  if (loading.value) return;
  
  loading.value = true;
  try {
    const moreData = await fetchMoreDataFromAPI();
    listData.value.push(...moreData);
  } finally {
    loading.value = false;
  }
};

// 监听数据变化
watch(listData, (newData) => {
  console.log('数据已更新，共', newData.length, '项');
}, { deep: true });
</script>

<template>
  <view>
    <VirtualList
      :data="listData"
      :height="400"
      :item-height="60"
      @scrollToBottom="loadMore"
    >
      <template #default="{ item, index }">
        <view class="list-item">
          {{ index }}: {{ item.title }}
        </view>
      </template>
      
      <!-- 空状态 -->
      <template #empty>
        <view class="empty-state">
          <text>暂无数据</text>
        </view>
      </template>
      
      <!-- 加载状态 -->
      <template #loading>
        <view class="loading-state">
          <text>加载中...</text>
        </view>
      </template>
    </VirtualList>
  </view>
</template>
```

## 最佳实践

### 1. 性能优化

```vue
<script setup lang="ts">
// 使用合适的缓冲区大小
const bufferSize = computed(() => {
  // 根据设备性能调整
  const systemInfo = uni.getSystemInfoSync();
  return systemInfo.platform === 'ios' ? 12 : 8;
});

// 优化数据结构
const optimizedData = computed(() => {
  return rawData.value.map(item => ({
    id: item.id,
    title: item.title,
    // 只保留必要的字段
  }));
});
</script>

<template>
  <VirtualList
    :data="optimizedData"
    :height="400"
    :item-height="60"
    :buffer-size="bufferSize"
    :enhanced="true"
  >
    <template #default="{ item, index }">
      <!-- 简化的模板结构 -->
      <view class="simple-item">
        {{ item.title }}
      </view>
    </template>
  </VirtualList>
</template>
```

### 2. 错误处理

```vue
<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';

const error = ref(null);

// 捕获错误
onErrorCaptured((err) => {
  console.error('虚拟列表错误:', err);
  error.value = err.message;
  return false;
});

// 重试机制
const retry = () => {
  error.value = null;
  loadData();
};
</script>

<template>
  <view>
    <view v-if="error" class="error-state">
      <text>{{ error }}</text>
      <button @click="retry">重试</button>
    </view>
    
    <VirtualList
      v-else
      :data="listData"
      :height="400"
      :item-height="60"
    >
      <template #default="{ item, index }">
        <view class="list-item">
          {{ item.title }}
        </view>
      </template>
    </VirtualList>
  </view>
</template>
```

## 注意事项

### 1. 微信小程序端
- 使用较大的缓冲区大小 (8-12)
- 关闭bounces属性
- 启用enhanced模式
- 保持固定的item高度

### 2. 数据要求
- 确保数据是数组格式
- 每个数据项最好有唯一的id
- 避免在渲染过程中修改数据

### 3. 样式建议
- 使用固定高度的列表项
- 避免复杂的CSS动画
- 使用简单的布局结构

### 4. 性能监控
```javascript
// 监控渲染性能
const startTime = performance.now();
// ... 渲染逻辑
const endTime = performance.now();
console.log('渲染耗时:', endTime - startTime, 'ms');
```
