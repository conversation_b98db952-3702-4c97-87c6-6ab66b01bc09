<template>
  <view class="virtual-list" :style="containerStyle">
    <!-- 空状态 -->
    <view v-if="isEmpty" class="virtual-list__empty">
      <slot name="empty">
        <view class="virtual-list__empty-default">
          <text class="virtual-list__empty-text">暂无数据</text>
        </view>
      </slot>
    </view>

    <!-- 加载状态 -->
    <view v-else-if="isLoading" class="virtual-list__loading">
      <slot name="loading">
        <view class="virtual-list__loading-default">
          <text class="virtual-list__loading-text">加载中...</text>
        </view>
      </slot>
    </view>

    <!-- 虚拟列表主体 -->
    <scroll-view
      v-else-if="shouldShowVirtualList"
      ref="scrollViewRef"
      class="virtual-list__scroll"
      :style="scrollViewStyle"
      :scroll-top="isInternalScrollUpdate ? currentScrollTop : undefined"
      :enhanced="enhanced"
      :bounces="bounces"
      :show-scrollbar="showScrollbar"
      :enable-back-to-top="enableBackToTop"
      :throttle="false"
      :enable-passive="true"
      scroll-y
      @scroll="handleScroll"
      @scrolltoupper="handleScrollToTop"
      @scrolltolower="handleScrollToBottom"
    >
      <!-- 总高度占位容器 -->
      <view class="virtual-list__placeholder" :style="placeholderStyle">
        <!-- 可视区域容器 -->
        <view class="virtual-list__viewport" :style="viewportStyle">
          <!-- 渲染的列表项 - 修复slot重复警告 -->
          <template v-for="(item, index) in renderedItems" :key="`vl-${item._virtualIndex}`">
            <view
              class="virtual-list__item"
              :style="getItemStyle(item, index)"
              :data-virtual-index="item._virtualIndex"
              :data-item-key="`vl-${item._virtualIndex}`"
            >
              <slot
                :item="item"
                :index="item._virtualIndex"
                :is-visible="item._isVisible"
                :name="`item-${item._virtualIndex}`"
              >
                <!-- 默认插槽内容 -->
                <view class="virtual-list__item-default">
                  {{ item }}
                </view>
              </slot>
            </view>
          </template>
        </view>
      </view>
    </scroll-view>

    <!-- 降级模式 - 普通列表 -->
    <scroll-view
      v-else-if="shouldShowFallbackList"
      ref="scrollViewRef"
      class="virtual-list__scroll virtual-list__fallback"
      :style="scrollViewStyle"
      :scroll-top="isInternalScrollUpdate ? currentScrollTop : undefined"
      :enhanced="enhanced"
      :bounces="bounces"
      :show-scrollbar="showScrollbar"
      :enable-back-to-top="enableBackToTop"
      :throttle="false"
      :enable-passive="true"
      scroll-y
      @scroll="handleScroll"
      @scrolltoupper="handleScrollToTop"
      @scrolltolower="handleScrollToBottom"
    >
      <!-- 直接渲染所有项目 - 降级模式 -->
      <template v-for="(item, index) in normalizedData" :key="`fallback-${index}`">
        <view
          class="virtual-list__item"
          :style="getItemStyle(item, index)"
          :data-fallback-index="index"
        >
          <slot :item="item" :index="index" :is-visible="true" :name="`fallback-item-${index}`">
            <!-- 默认插槽内容 -->
            <view class="virtual-list__item-default">
              {{ item }}
            </view>
          </slot>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { type CSSProperties, computed, onMounted, reactive, ref, watch } from 'vue';
import type { ScrollEvent, VirtualListEvents, VirtualListProps } from './types';
import { useVirtualList } from './hooks/useVirtualList';
import { useScrollManager } from './hooks/useScrollManager';
import { usePlatformOptimize } from './hooks/usePlatformOptimize';
import { VirtualListErrorHandler } from './utils/error-handler';
import { VirtualListDataValidator } from './utils/data-validator';

// Props定义 - 使用具体的接口定义而不是泛型
interface Props {
  // 数据源 - 必需
  data: Array<any>;
  /**
   * 容器尺寸
   * @param width 宽度
   * @param height 高度
   */
  width?: string | number;
  height: string | number;
  /**
   * 列表项配置
   */
  itemHeight: number;
  dynamicHeight?: boolean;
  /**
   * 性能优化
   * @param bufferSize 缓冲区大小
   * @param threshold 阈值
   */
  bufferSize?: number;
  threshold?: number;
  /**
   * 滚动配置
   * @param scrollTop 滚动位置
   * @param enableBackToTop 是否启用回到顶部
   */
  scrollTop?: number;
  enableBackToTop?: boolean;

  /**
   * 平台特定
   * @param enhanced 是否启用性能优化
   * @param bounces 是否启用回弹
   * @param showScrollbar 是否显示滚动条
   */
  enhanced?: boolean;
  bounces?: boolean;
  showScrollbar?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  // 容器尺寸
  width: '100%',
  // 列表项配置
  dynamicHeight: false,
  // 性能优化
  bufferSize: 5,
  threshold: 100,
  // 滚动配置
  scrollTop: 0,
  enableBackToTop: false,
  // 平台特定
  enhanced: true,
  bounces: true,
  showScrollbar: true
});

// Events定义 - 必须在props之前
const emit = defineEmits<{
  scroll: [event: VirtualListEvents['scroll'][0]];
  scrollToTop: [];
  scrollToBottom: [];
  itemVisible: [index: number, item: any];
  itemHidden: [index: number, item: any];
}>();

// 错误处理器和数据验证器
const errorHandler = new VirtualListErrorHandler();
const dataValidator = new VirtualListDataValidator(errorHandler);

// 验证和规范化的数据
const normalizedData = ref<any[]>([]);
const normalizedConfig = ref<Partial<VirtualListProps>>({});
const shouldUseVirtualization = ref(true);
const validationWarnings = ref<string[]>([]);

// Props验证和规范化
const validateAndNormalizeProps = () => {
  try {
    // 验证和规范化数据
    const dataResult = dataValidator.validateAndNormalizeData(props.data);

    if (!dataResult.isValid && dataResult.error) {
      console.error('VirtualList: Data validation failed', dataResult.error);
      normalizedData.value = dataResult.normalizedData || [];
    } else {
      normalizedData.value = dataResult.normalizedData || [];
    }

    // 验证和规范化配置
    const configResult = dataValidator.validateAndNormalizeConfig(props);
    if (!configResult.isValid && configResult.error) {
      console.error('VirtualList: Config validation failed', configResult.error);
    }
    normalizedConfig.value = configResult.normalizedConfig || {};

    // 处理边界情况
    const edgeCaseResult = dataValidator.handleEdgeCases(
      normalizedData.value,
      normalizedConfig.value
    );
    shouldUseVirtualization.value = edgeCaseResult.shouldUseVirtualization;
    validationWarnings.value = edgeCaseResult.warnings;

    // 应用优化后的配置
    Object.assign(normalizedConfig.value, edgeCaseResult.optimizedConfig);

    // 输出警告信息
    if (validationWarnings.value.length > 0) {
      console.warn('VirtualList: Edge case warnings:', validationWarnings.value);
    }

    return true;
  } catch (error) {
    console.error('VirtualList: Props validation failed', error);
    return false;
  }
};

// 创建响应式的props对象用于hooks
const reactiveProps = reactive({
  data: normalizedData.value,
  height: props.height,
  width: props.width,
  itemHeight: props.itemHeight,
  dynamicHeight: props.dynamicHeight,
  bufferSize: props.bufferSize,
  threshold: props.threshold,
  scrollTop: props.scrollTop,
  enableBackToTop: props.enableBackToTop,
  enhanced: props.enhanced,
  bounces: props.bounces,
  showScrollbar: props.showScrollbar
});

// 集成核心Hooks - 使用响应式props
const virtualListHook = useVirtualList(reactiveProps);

const scrollManagerHook = useScrollManager({
  onScroll: (event: ScrollEvent) => {
    emit('scroll', event);
    checkVisibilityChanges();
  },
  onScrollToTop: () => {
    emit('scrollToTop');
  },
  onScrollToBottom: () => {
    emit('scrollToBottom');
  }
});

const platformOptimizeHook = usePlatformOptimize({
  enableImageLazyLoad: true,
  enablePerformanceMonitoring: false
});

// 基础响应式数据
const isLoading = ref(false);
const lastVisibleItems = ref<Set<number>>(new Set());

// scroll-view引用
const scrollViewRef = ref<any>(null);
const currentScrollTop = ref(0);
const isInternalScrollUpdate = ref(false);

// 设置滚动位置的内部方法（需要在使用前定义）
const setScrollTop = (scrollTop: number) => {
  const newScrollTop = Math.max(0, scrollTop);

  // 标记为内部更新，防止循环
  isInternalScrollUpdate.value = true;
  currentScrollTop.value = newScrollTop;

  // 延迟重置标记
  setTimeout(() => {
    isInternalScrollUpdate.value = false;
  }, 100);
};

// 从hooks获取数据
const {
  visibleItems,
  renderState,
  placeholderStyle: hookPlaceholderStyle,
  visibleAreaStyle
} = virtualListHook;
const { scrollTop: _managerScrollTop } = scrollManagerHook;
// 平台优化hook的返回值暂时不使用
// const {} = platformOptimizeHook;

// 渲染的项目（适配模板需要的格式）
const renderedItems = computed(() => {
  return visibleItems.value || [];
});

// 组件挂载时验证和规范化props
onMounted(() => {
  if (!validateAndNormalizeProps()) {
    console.error('VirtualList: Props validation and normalization failed');
  }
  // 更新响应式props
  reactiveProps.data = normalizedData.value;

  // 设置滚动管理器的回调
  if (scrollManagerHook && scrollManagerHook.setScrollToCallback) {
    scrollManagerHook.setScrollToCallback(setScrollTop);
  }
});

// 监听props变化 - 重新验证和规范化
watch(
  () => props.data,
  () => {
    validateAndNormalizeProps();
    reactiveProps.data = normalizedData.value;
    if (virtualListHook.handleDataChange) {
      virtualListHook.handleDataChange();
    }
  },
  { deep: true }
);

watch(
  [() => props.height, () => props.itemHeight, () => props.bufferSize, () => props.threshold],
  () => {
    validateAndNormalizeProps();
    reactiveProps.height = props.height;
    reactiveProps.itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
    reactiveProps.bufferSize = normalizedConfig.value.bufferSize || props.bufferSize;
    reactiveProps.threshold = normalizedConfig.value.threshold || props.threshold;
  }
);

watch(
  () => props.scrollTop,
  (newScrollTop) => {
    reactiveProps.scrollTop = newScrollTop;
    if (newScrollTop !== currentScrollTop.value) {
      scrollManagerHook.setScrollTop(newScrollTop);
    }
  }
);

watch(
  () => props.itemHeight,
  () => {
    reactiveProps.itemHeight = props.itemHeight;
    if (virtualListHook.handleItemHeightChange) {
      virtualListHook.handleItemHeightChange();
    }
  }
);

watch([() => props.width, () => props.height], () => {
  reactiveProps.width = props.width;
  reactiveProps.height = props.height;
  if (virtualListHook.handleContainerResize) {
    virtualListHook.handleContainerResize();
  }
});

// 计算属性 - 容器样式
const containerStyle = computed(
  (): CSSProperties => ({
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height
  })
);

// 计算属性 - 滚动视图样式
const scrollViewStyle = computed((): CSSProperties => {
  const baseStyle: CSSProperties = {
    width: '100%',
    height: '100%'
  };

  // 应用平台优化样式
  const platformStyles = platformOptimizeHook.getPlatformStyles();
  return { ...baseStyle, ...platformStyles };
});

// 计算属性 - 占位容器样式（总高度）
const placeholderStyle = computed((): CSSProperties => {
  return (
    hookPlaceholderStyle.value || {
      height: `${getTotalHeight()}px`,
      position: 'relative'
    }
  );
});

// 计算属性 - 可视区域容器样式
const viewportStyle = computed((): CSSProperties => {
  return (
    visibleAreaStyle.value || {
      transform: `translateY(${getViewportOffset()}px)`,
      position: 'absolute',
      top: '0',
      left: '0',
      right: '0'
    }
  );
});

// 计算属性 - 是否为空状态
const isEmpty = computed(() => {
  return !normalizedData.value || normalizedData.value.length === 0;
});

// 计算属性 - 是否应该显示虚拟列表
const shouldShowVirtualList = computed(() => {
  return !isEmpty.value && !isLoading.value && shouldUseVirtualization.value;
});

// 计算属性 - 是否应该显示普通列表（降级模式）
const shouldShowFallbackList = computed(() => {
  return !isEmpty.value && !isLoading.value && !shouldUseVirtualization.value;
});

// 基础方法 - 获取总高度
function getTotalHeight(): number {
  if (!normalizedData.value) return 0;
  const itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
  return normalizedData.value.length * itemHeight;
}

// 基础方法 - 获取可视区域偏移
function getViewportOffset(): number {
  const itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
  return renderState.value?.bufferedStart ? renderState.value.bufferedStart * itemHeight : 0;
}

// 基础方法 - 获取列表项样式
function getItemStyle(_item: any, _index: number): CSSProperties {
  const itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
  const baseStyle: CSSProperties = {
    height: `${itemHeight}px`,
    position: 'relative'
  };

  // 应用渲染优化
  const renderOptimizations = platformOptimizeHook.applyRenderOptimizations();
  if (renderOptimizations.useTransform) {
    baseStyle.backfaceVisibility = 'hidden';
  }
  if (renderOptimizations.willChange) {
    baseStyle.willChange = 'transform';
  }

  return baseStyle;
}

// 滚动事件节流控制
let scrollTimer: number | null = null;
let lastScrollTime = 0;
// 使用适中的节流延迟，兼容各平台
const SCROLL_THROTTLE_DELAY = 30; // 30ms节流，平衡性能和流畅度

// 事件处理 - 滚动事件
function handleScroll(event: ScrollEvent) {
  // 防止内部更新导致的循环滚动
  if (isInternalScrollUpdate.value) {
    return;
  }

  const now = Date.now();
  const scrollTop = event.detail.scrollTop;

  // 立即更新滚动位置，避免视觉延迟
  if (virtualListHook.calculator.value) {
    virtualListHook.calculator.value.updateScrollTop(scrollTop);
  }

  // 节流处理其他逻辑
  if (now - lastScrollTime >= SCROLL_THROTTLE_DELAY) {
    lastScrollTime = now;

    // 处理滚动逻辑
    processScrollEvent(event);
  } else {
    // 清除之前的定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer);
    }

    // 延迟执行
    scrollTimer = setTimeout(
      () => {
        processScrollEvent(event);
        lastScrollTime = Date.now();
      },
      SCROLL_THROTTLE_DELAY - (now - lastScrollTime)
    ) as unknown as number;
  }
}

// 处理滚动事件的核心逻辑
function processScrollEvent(event: ScrollEvent) {
  try {
    // 使用scrollManager处理滚动
    scrollManagerHook.handleScroll(event);

    // 更新虚拟列表的渲染范围
    if (virtualListHook.calculator.value) {
      virtualListHook.calculateRenderRange();
    }
  } catch (error) {
    console.error('VirtualList: Error in processScrollEvent', error);
  }
}

// 事件处理 - 滚动到顶部
function handleScrollToTop() {
  emit('scrollToTop');
}

// 事件处理 - 滚动到底部
function handleScrollToBottom() {
  emit('scrollToBottom');
}

// 检查可见性变化
function checkVisibilityChanges() {
  try {
    const currentVisibleSet = new Set<number>();

    // 安全地遍历渲染项
    if (renderedItems.value && Array.isArray(renderedItems.value)) {
      renderedItems.value.forEach((item) => {
        if (item && item._isVisible && typeof item._virtualIndex === 'number') {
          currentVisibleSet.add(item._virtualIndex);
        }
      });
    }

    // 检查新出现的项目
    currentVisibleSet.forEach((index) => {
      if (!lastVisibleItems.value.has(index)) {
        // 确保索引有效
        if (index >= 0 && index < normalizedData.value.length) {
          const item = normalizedData.value[index];
          emit('itemVisible', index, item);
        }
      }
    });

    // 检查消失的项目
    lastVisibleItems.value.forEach((index) => {
      if (!currentVisibleSet.has(index)) {
        // 确保索引有效
        if (index >= 0 && index < normalizedData.value.length) {
          const item = normalizedData.value[index];
          emit('itemHidden', index, item);
        }
      }
    });

    lastVisibleItems.value = currentVisibleSet;
  } catch (error) {
    console.error('VirtualList: Error in checkVisibilityChanges', error);
  }
}

// 暴露方法给父组件
const scrollToIndex = (index: number) => {
  if (index < 0 || index >= normalizedData.value.length) {
    console.warn('VirtualList: Invalid scroll index', index);
    return;
  }

  const itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
  const targetScrollTop = index * itemHeight;
  setScrollTop(targetScrollTop);
};

const scrollToTop = () => {
  setScrollTop(0);
};

const scrollToBottom = () => {
  const itemHeight = normalizedConfig.value.itemHeight || props.itemHeight;
  const totalHeight = normalizedData.value.length * itemHeight;
  const containerHeight =
    typeof props.height === 'number' ? props.height : parseFloat(props.height) || 0;
  const maxScrollTop = Math.max(0, totalHeight - containerHeight);
  setScrollTop(maxScrollTop);
};

// 暴露给模板引用
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  getItemPosition: virtualListHook.getItemPosition,
  isIndexVisible: virtualListHook.isIndexVisible
});
</script>

<style scoped>
.virtual-list {
  position: relative;
  overflow: hidden;
}

.virtual-list__scroll {
  width: 100%;
  height: 100%;
}

.virtual-list__placeholder {
  position: relative;
  width: 100%;
}

.virtual-list__viewport {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  will-change: transform;
}

.virtual-list__item {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.virtual-list__item-default {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

/* 空状态样式 */
.virtual-list__empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.virtual-list__empty-default {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.virtual-list__empty-text {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

/* 加载状态样式 */
.virtual-list__loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.virtual-list__loading-default {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.virtual-list__loading-text {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
}

/* 性能优化样式 */
.virtual-list__item {
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 平台特定样式 */
.virtual-list__scroll {
  -webkit-overflow-scrolling: touch; /* iOS滚动优化 */
}

/* 降级模式样式 */
.virtual-list__fallback {
  /* 降级模式下的特殊样式 */
  background-color: transparent;
}

/* 隐藏滚动条（H5端） */
.virtual-list__scroll::-webkit-scrollbar {
  display: none;
}

.virtual-list__scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
