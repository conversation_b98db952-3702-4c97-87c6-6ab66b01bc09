<template>
  <canvas
    id="lottie_canvas"
    type="2d"
    canvas-id="lottie_canvas"
    :class="canvasClass"
    :style="canvasStyle"
  />
</template>

<script setup lang="ts">
// #ifdef MP-WEIXIN
import lottie from 'lottie-miniprogram';
// #endif

interface Props {
  animationData: any;
  width?: string | number;
  height?: string | number;
  loop?: boolean;
  autoplay?: boolean;
  canvasClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  width: '250px',
  height: '120px',
  loop: true,
  autoplay: true,
  canvasClass: ''
});

const canvasStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    maxWidth: typeof props.width === 'number' ? `${props.width}px` : props.width,
    maxHeight: typeof props.height === 'number' ? `${props.height}px` : props.height
  };
});

let ani: any = null;

// 获取设备像素比
const getDevicePixelRatio = (): number => {
  let dpr = 1;
  // #ifdef MP-WEIXIN
  dpr = uni.getSystemInfoSync().pixelRatio;
  // #endif
  return dpr;
};

// 安全设置canvas尺寸的辅助函数
const setupCanvasSafely = (canvas: any, context: any) => {
  const dpr = getDevicePixelRatio();
  const maxCanvasSize = 8192; // 小程序canvas最大尺寸限制

  // 获取原始尺寸
  const originalWidth = canvas.width;
  const originalHeight = canvas.height;

  // 计算目标尺寸，确保不超过限制
  const targetWidth = Math.min(originalWidth * dpr, maxCanvasSize);
  const targetHeight = Math.min(originalHeight * dpr, maxCanvasSize);

  // 如果计算出的尺寸会超过限制，使用安全的默认值
  const safeWidth =
    originalWidth * dpr > maxCanvasSize
      ? Math.min(parseInt(String(props.width)) * dpr, maxCanvasSize)
      : targetWidth;
  const safeHeight =
    originalHeight * dpr > maxCanvasSize
      ? Math.min(parseInt(String(props.height)) * dpr, maxCanvasSize)
      : targetHeight;

  // 设置canvas尺寸
  canvas.width = safeWidth;
  canvas.height = safeHeight;
  context.scale(dpr, dpr);

  return { width: safeWidth, height: safeHeight };
};

// 初始化Lottie动画
const initLottie = () => {
  // #ifdef MP-WEIXIN
  const canvasNode = uni.createSelectorQuery().select('#lottie_canvas');
  if (canvasNode) {
    canvasNode
      .node((res) => {
        if (res && res.node) {
          const canvas = res.node;
          try {
            const context = canvas.getContext('2d');

            // 使用安全的canvas尺寸设置函数
            setupCanvasSafely(canvas, context);

            lottie.setup(canvas);
            ani = lottie.loadAnimation({
              loop: props.loop,
              autoplay: props.autoplay,
              animationData: props.animationData,
              rendererSettings: {
                context
              }
            });
          } catch (error) {
            console.error('Canvas setup error:', error);
          }
        }
      })
      .exec();
  }
  // #endif
};

// 监听动画数据变化，重新加载动画
watch(
  () => props.animationData,
  () => {
    if (ani) {
      ani.destroy();
    }
    nextTick(() => {
      initLottie();
    });
  },
  { deep: true }
);

onMounted(() => {
  initLottie();
});

onBeforeUnmount(() => {
  if (ani) {
    ani.destroy();
    ani = null;
  }
});

// 添加默认导出
defineExpose({
  // 可以在这里暴露一些方法给父组件使用
  destroy: () => {
    if (ani) {
      ani.destroy();
      ani = null;
    }
  },
  play: () => {
    if (ani) {
      ani.play();
    }
  },
  pause: () => {
    if (ani) {
      ani.pause();
    }
  },
  stop: () => {
    if (ani) {
      ani.stop();
    }
  }
});
</script>

<style lang="scss" scoped>
canvas {
  display: block;
}
</style>

<script lang="ts">
export default {
  name: 'XxtLottie'
};
</script>
