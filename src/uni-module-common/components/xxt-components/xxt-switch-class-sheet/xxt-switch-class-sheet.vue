<template>
  <tui-actionsheet
    :show="rotated"
    :item-list="classListSheet"
    tips="切换班级"
    @click="itemClick"
    @cancel="emit('update:rotated', false)"
  >
  </tui-actionsheet>
</template>

<script setup lang="ts">
import type { classListType } from '@/uni-module-common/components/xxt-components/xxt-switch-class/hooks/switchClassStore';
import useSwitchClassStore from '@/uni-module-common/components/xxt-components/xxt-switch-class/hooks/switchClassStore';
import { getUserAuthGroup } from '@/uni-module-common/components/xxt-components/xxt-switch-class/hooks/requestApi';
const props = withDefaults(
  defineProps<{
    switchClassInfo: classListType;
    rotated: boolean;
  }>(),
  {
    switchClassInfo: () => ({}),
    rotated: false
  }
);
const emit = defineEmits<{
  (e: 'update:rotated', val: boolean): void;
}>();
const { isLogin, userInfo } = useStore('user');

const switchClassStore = useSwitchClassStore();
const classList = ref<any[]>([]);
const classListSheet = computed(() => {
  const webId = userInfo.value.wid;
  let list: any = [];
  if (webId) {
    console.log('circleStore.homeMainInfo----', props.switchClassInfo);
    const circleInfo = props.switchClassInfo;
    console.log('circleStore.backgroundMethod-----', circleInfo);
    if (circleInfo) {
      const classId = circleInfo.classId;
      list = classList.value.map((item) => ({
        text: item.className,
        color: `${item.classId}` === `${classId}` ? '#4AD975' : '#222222',
        classId: (item.classId || '').toString(),
        isAdmin: item.isAdmin,
        isBookListing: item.isBookListing
      }));
    }
  }
  return list;
});
const itemClick = (e: any) => {
  const classId = e.classId;
  const isAdmin = e.isAdmin;
  const className = e.text;
  const isBookListing = e.isBookListing;

  const obj: any = {};
  const wid = userInfo.value.wid;
  if (wid) {
    obj[wid] = {
      classId,
      isAdmin,
      className,
      jut: userInfo.value.jut,
      isBookListing
    };
    console.log('circleStore.circleInfo---clickSelectUnit--', obj);
    switchClassStore.setSwitchClassInfo(obj);
  }
  const index = e.index;
  console.log(e, index);
  emit('update:rotated', false);
};

const switchRulesReloadData = async () => {
  // 从circle中获取store
  let storeBooksListingInfo = {};
  if (userInfo.value.wid) {
    storeBooksListingInfo = switchClassStore.switchClassInfo[userInfo!.value!.wid] || {};
  }
  const teacherGroupInfoList: any = await getUserAuthGroup({});
  classList.value = teacherGroupInfoList.groupInfos.map((item: any) => ({
    className: item.groupName,
    classId: item.groupId,
    isAdmin: item.admin, // true 管理员 false 非管理员
    isBookListing: item.device, // true有设备false无设备
    groupType: item.groupType // 组织类型
  }));
  let classId = '';
  let isAdmin = false;
  let className = '';
  let isBookListing = false;
  // classList  的数据更新是异步的。所以需要nextTick去数据同步操作
  await nextTick();
  // 如果storeCircleInfo没有值，则说明是第一次初始化
  if (Object.keys(storeBooksListingInfo).length <= 0 && userInfo.value.wid) {
    if (classList.value.length > 0) {
      classId = (classList.value[0].classId || '').toString();
      isAdmin = classList.value[0].isAdmin;
      className = classList.value[0].className;
      isBookListing = classList.value[0].isBookListing;
    }
    const obj: any = {};
    const wid = userInfo.value.wid;
    obj[wid] = {
      classId,
      isAdmin,
      className,
      jut: userInfo.value.jut,
      isBookListing
    };
    switchClassStore.setSwitchClassInfo(obj);
  }
  console.log('storeBooksListingInfo---ipdate---', switchClassStore.switchClassInfo);
  console.log('storeBooksListingInfo---ipdate-----managerList-----', classList.value);
};
watch(
  () => userInfo.value.wid,
  async (newVal, oldVal) => {
    // 重制圈子信息
    // circleStore.resetCircleInfo();
    // 初始化圈子信息
    console.log('userInfo.value.wid---', newVal, oldVal, userInfo.value.wid);
    if (userInfo.value.wid) {
      switchRulesReloadData();
    }
  },
  { deep: true, immediate: true }
);
</script>

<style scoped lang="scss"></style>
