<template>
  <view class="root-view">
    <tui-modal :show="showModal" custom padding="32rpx" width="79%">
      <view class="tui-modal-custom">
        <tui-text
          color="#222222"
          padding="0 0 32rpx 0"
          :font-weight="900"
          align="center"
          block
          :text="modalTitle"
        ></tui-text>
        <slot name="modalContent" />
        <template v-if="isCustomeFooter">
          <slot name="modalFooter"></slot>
        </template>
        <template v-else>
          <view
            class="thorui-flex__between"
            :style="oneButtonStyle"
            style="gap: 0 38rpx; margin-top: 32rpx"
          >
            <tui-form-button
              v-if="isShowCancelBtn"
              radius="50px"
              color="#4AD975"
              :width="btnWidth"
              :height="btnHeight"
              plain
              border-color="#4AD975"
              bold
              @click="closeClick"
              >{{ cancelText }}</tui-form-button
            >
            <tui-form-button
              v-if="isShowConfirmBtn"
              radius="50px"
              color="#FFFFFF"
              :width="btnWidth"
              :height="btnHeight"
              background="#4AD975"
              bold
              @click="confirmClick"
              >{{ confirmText }}</tui-form-button
            >
          </view>
        </template>
      </view>
    </tui-modal>
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    showModal: boolean;
    modalTitle?: string; // 弹窗标题
    isCustomeFooter?: boolean; // 是否自定义底部
    cancelText?: string; // 取消按钮文本
    confirmText?: string; // 确认按钮文本
    isShowCancelBtn?: boolean; // 是否显示取消按钮
    isShowConfirmBtn?: boolean; // 是否显示确认按钮
    btnWidth?: string; // 按钮宽度
    btnHeight?: string;
  }>(),
  {
    showModal: false,
    modalTitle: '',
    isCustomeFooter: false,
    cancelText: '取消',
    confirmText: '确定',
    isShowCancelBtn: true,
    isShowConfirmBtn: true,
    btnWidth: '246rpx',
    btnHeight: '72rpx'
  }
);
const emits = defineEmits<{ (e: 'closeClick'): void; (e: 'confirmClick'): void }>();

const oneButtonStyle = computed(() => {
  if (!props.isShowCancelBtn || !props.isShowConfirmBtn) {
    return {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    };
  } else {
    return {};
  }
});
const closeClick = () => {
  emits('closeClick');
};
const confirmClick = () => {
  emits('confirmClick');
};
</script>

<style scoped></style>
