import { cloneDeep } from 'lodash';
import event from '@/uni-module-common/utils/eventBus';
export interface classListType {
  classId?: string; // 班级id
  className?: string; // 班级名称
  isAdmin?: boolean; // 是否是管理员
  jut?: number; // 用户角色 // 角色 // jxlx身份类型 -1, "网站注册账号" 0, "教师" 1, "学生" 2, "家长" 3, "管理员"  99, "访客（未登录）"
  isBookListing?: boolean; // 是否有图书柜
}
type classListTypeKey = keyof classListType;
// 定义一个索引签名类型
interface SwitchClassObject {
  [key: string]: classListType; // 这里的 key 可以是任意字符串 主要是用户的wid
}
// 默认的选择的班级信息
const defaultSwitchClassInfo: SwitchClassObject = {
  '': {
    classId: '',
    className: '',
    isAdmin: false,
    jut: 0,
    isBookListing: false
  }
};

const switchClassInfo = defineStore({
  id: 'switchClassInfo',
  state: () => {
    console.log('initSwitchClassDataList-----store数据---');
    return {
      switchClassInfo: cloneDeep(defaultSwitchClassInfo) // 班级信息
    };
  },
  actions: {
    setSwitchClassInfo(data: any) {
      console.log('initSwitchClassDataList-----setSwitchClassInfo---', data);
      this.switchClassInfo = { ...this.switchClassInfo, ...data };
      // 切换班级后需要对搜索条件初始化，并在内部设置班级id， 需要将事件传递出去
      event.emit('switchClassEvent', data);
    },
    // 重置圈子信息
    resetSwitchClassInfo() {
      this.switchClassInfo = cloneDeep(defaultSwitchClassInfo);
    },
    // 根据wid获取圈子信息，修改圈子信息
    setSwitchClassInfoByWid(wid: string, key: classListTypeKey, value: any) {
      if (this.switchClassInfo[wid]) {
        this.switchClassInfo[wid][key] = value;
      }
    }
    // resetAppConfigData() {
    //   this.appConfig = cloneDeep(appModuleConfig.defalutAppConfig);
    //   console.log('resetAppConfigData-----', this.appConfig);
    //   console.log('resetAppConfigData-----defalutAppConfig---', appModuleConfig.defalutAppConfig);
    // },
  },
  persist: {
    enabled: true,
    H5Storage: window?.localStorage,
    strategies: [
      {
        // 之前的
        // storage: window?.localStorage
        //  存储在本地
        storage: {
          getItem: (key: string) => {
            return uni.getStorageSync(key);
          },
          setItem: (key: string, value: string) => {
            uni.setStorageSync(key, value);
          },
          removeItem: (key: string) => {
            uni.removeStorageSync(key);
          },
          length: 1,
          key: (index: number) => {
            console.log('key--', index);
            return 'switchClassInfo';
          },
          clear: () => {
            uni.clearStorageSync();
          }
        }
        // paths: ['userInfo']
      }
    ]
  }
});
export default switchClassInfo;
