import {
  errorStrFunc,
  referer,
  setWXLoginCookie,
  uploadFileCBError,
  uploadFileCBSuccess,
  userAgentPrefix
} from './index';
import { apiUrlPrefix, uploadBaseUrl } from '@/uni-module-common/config/';
import { variableTypeDetection } from '@/uni-module-common/utils/verifyType';
import eventBus from '@/uni-module-common/utils/eventBus';
import type OssManagerClassType from '@/uni-module-common/hooks/oss-upload/ossManagerClass';

// 单位 Bytes转换为kb 1 KB = 1024 Bytes
const bytesToKb = (bytes: number) => {
  // 返回也是number
  return Number((bytes / 1024).toFixed(2));
};

const newUploadFilePromise = (
  filePath: string,
  primiFilePath: string,
  fileTypeNum: number,
  retryCount: number,
  formData: any,
  urlApi: string,
  itemSeq?: number, // 兼容填表打卡，区分是第几个表单项的附件
  ossManagerClass?: OssManagerClassType
) => {
  const { isStrategyStop }: any = useStore('fileUpload');
  // 日志上传
  const logFormData = {
    ...formData,
    singleFileIdentity: ossManagerClass?.singleFileIdentity,
    fileName: filePath,
    // 上传方式 1 移动云 2 阿里云 3 公司
    uploadType: 3
  };
  !isStrategyStop.value && ossManagerClass?.saveOssUploadResultLog(logFormData, false);

  const token = uni.getStorageSync('token');
  const errorStr = errorStrFunc(fileTypeNum);
  let uploadFileAPI = '';
  if (urlApi.startsWith('http') || urlApi.startsWith('https')) {
    uploadFileAPI = urlApi;
  } else {
    uploadFileAPI = `${uploadBaseUrl}${apiUrlPrefix}${urlApi}`;
  }
  console.log(
    '--------------------------------这是新的上传逻辑（策略模式）--------------------------------'
  );
  console.log(
    'updateUploadUrlApi-----uploadFileAPI------isStrategyStop----1',
    isStrategyStop.value
  );
  console.log('updateUploadUrlApi-----uploadFileAPI------', uploadFileAPI);
  console.log('updateUploadUrlApi-----uploadFileAPI------token----', token);
  console.log('updateUploadUrlApi-----uploadFileAPI------formData----', formData);
  console.log('updateUploadUrlApi-----uploadFileAPI------filePath----', filePath);
  console.log('updateUploadUrlApi-----uploadFileAPI------itemSeq----', itemSeq);
  console.log('updateUploadUrlApi-----uploadFileAPI------ossManagerClass----', ossManagerClass);
  let header = {
    Cookie: token,
    Referer: referer,
    'User-Agent': userAgentPrefix
  };
  if (variableTypeDetection.isObject(token)) {
    header = setWXLoginCookie(token, header);
  }
  // 公共上传逻辑，去除retryCount的三次重试机会，只传一次，解决服务器多次上传占用资源问题
  retryCount = 0;
  console.log('updateUploadUrlApi-----uploadFileAPI------header----', header);

  // 当前本地时间
  const localTime = new Date();
  // 记录开始上传时间
  let recordStartTime = new Date();
  // 记录已经上传的数据长度，单位 Bytes
  const recordTotalBytesSent = 0;
  let isStrategyUploadExecuted = false;
  // 文件大小
  let fileSize = 0;
  // 上传的文件大小
  const uploadFileSize = 0;

  return new Promise<{ fileId: string; filePath: string } | ''>((resolve, reject) => {
    console.log(
      'updateUploadUrlApi-----uploadFileAPI------isStrategyStop----2',
      isStrategyStop.value
    );
    // 如果已经达到策略则直接使用阿里云策略上传
    if (isStrategyStop.value) {
      console.log('updateUploadUrlApi-----开始进入预判测试进入阿里云上传', isStrategyStop.value);
      isStrategyUploadExecuted = true;
      strategyUpload(
        filePath,
        primiFilePath,
        fileTypeNum,
        retryCount,
        formData,
        urlApi,
        resolve,
        reject,
        false,
        {
          localTime: recordStartTime,
          fileSize,
          ossManagerClass,
          isOld: false,
          uploadFileSize
        },
        itemSeq,
        ossManagerClass,
        errorStr
      );
      return;
    }
    // const start = new Date(); // 记录开始时间
    // console.info('开始上传附件时间---', start);
    const uploadTask = uni.uploadFile({
      url: uploadFileAPI,
      filePath,
      name: 'file',
      formData,
      // #ifdef MP-WEIXIN
      timeout: 6000000, // 超时时间600s 10分钟 取消超时实践 // 如果不设置则可能会遭到默认60s的超时时间 改100分钟
      // #endif
      header,
      success: (resData: any) => {
        const res = JSON.parse(resData.data);
        const obj = {
          res,
          filePath,
          primiFilePath,
          fileTypeNum,
          retryCount,
          formData,
          urlApi,
          itemSeq,
          resolve,
          reject,
          logInfo: {
            localTime: recordStartTime,
            fileSize,
            ossManagerClass,
            isOld: false,
            uploadFileSize
          }
        };
        uploadFileCBSuccess(obj);
      },
      fail: (err) => {
        const obj = {
          err,
          filePath,
          primiFilePath,
          fileTypeNum,
          retryCount,
          formData,
          urlApi,
          resolve,
          reject,
          errorStr,
          logInfo: {
            localTime: recordStartTime,
            fileSize,
            ossManagerClass,
            isOld: false,
            uploadFileSize
          }
        };
        console.log('updateUploadUrlApi-----uploadFileAPI------err----文件上传失败---', err);
        console.log(
          'updateUploadUrlApi-----uploadFileAPI------isStrategyStop----3',
          isStrategyStop.value
        );
        !isStrategyStop.value && uploadFileCBError(obj); // 上传失败记录日志
        // 如果重试次数为0的话则取消上传任务
        if (retryCount === 0 && !isStrategyStop.value) {
          if (uploadTask && typeof uploadTask.abort === 'function') {
            try {
              uploadTask.abort();
            } catch (error) {
              console.error('终止上传任务时发生错误:', error);
            }
          }
        }
        // 如果是达到策略中断上传 则进行阿里云oss上传
        if (isStrategyStop.value) {
          console.log('updateUploadUrlApi-----测试进入阿里云上传', isStrategyStop.value);
          // 切换阿里云上传，需要更新记录日志
          ossManagerClass?.updateOssUploadResultLog(
            formData,
            recordStartTime,
            0,
            fileSize,
            false,
            uploadFileSize,
            errorStr,
            ''
          );
          console.log('updateUploadUrlApi-----测试进入阿里云上传日志已更新', isStrategyStop.value);
          if (!isStrategyUploadExecuted) {
            console.log('进上传失败调用策略上传');
            isStrategyUploadExecuted = true;
            strategyUpload(
              filePath,
              primiFilePath,
              fileTypeNum,
              retryCount,
              formData,
              urlApi,
              resolve,
              reject,
              true,
              {
                localTime: recordStartTime,
                fileSize,
                ossManagerClass,
                isOld: false,
                uploadFileSize
              },
              itemSeq,
              ossManagerClass,
              errorStr
            );
          }
        }
      }
    });
    eventBus.emit('unloadOnUploadFile', {
      uploadTask
    });
    // 3. 监听进度（关键修复：强制状态更新）
    uploadTask.onProgressUpdate(async (res) => {
      const lastBytesSent = (uploadTask as any)._lastBytesSent || 0;
      const now = Date.now();
      // 第一次进入时初始化 startTime
      const startTime = res.totalBytesSent === 0 ? now : (uploadTask as any)._startTime || now;
      (uploadTask as any)._startTime = startTime;
      const timeDiff = (now - startTime) / 1000;
      const uploadSpeed = bytesToKb(res.totalBytesSent - lastBytesSent) / timeDiff;

      console.log(`onProgressUpdate---上传进度${res.progress}`);
      console.log(`onProgressUpdate---已经上传的数据长度${res.totalBytesSent}`);
      console.log(`onProgressUpdate---预期需要上传的数据总长度${res.totalBytesExpectedToSend}`);
      console.log(`onProgressUpdate---进入的本地当前时间${localTime}`);
      console.log(`onProgressUpdate---进入的开始上传时间${localTime}`);
      console.log(
        'onProgressUpdate---策略返回的秒数----',
        ossManagerClass?.ossUploadConfig.uploadTimeScope
      );
      // 上传回掉后本地时间
      const localTimeAfter = new Date();
      console.log('onProgressUpdate---上传回掉后本地时间----localTimeAfter---', localTimeAfter);
      // 计算这段时间 转换成秒
      const timeDifference = (localTimeAfter.getTime() - localTime.getTime()) / 1000;
      recordStartTime = localTime;
      fileSize = res.totalBytesExpectedToSend;
      console.log('onProgressUpdate---上传回掉后本地时间----timeDifference---', timeDifference);
      // 判断时间是否达到策略时间 并且上传进度在策略进度之下
      if (
        timeDifference >= (ossManagerClass?.ossUploadConfig.uploadTimeScope ?? 0) &&
        res.progress < (ossManagerClass?.ossUploadConfig.uploadProgress ?? 0) &&
        !isStrategyStop.value &&
        uploadSpeed <= (ossManagerClass?.ossUploadConfig.uploadSpeed ?? 0)
      ) {
        console.log('已达到策略条件，切换至阿里云OSS上传');
        const { updateIsStrategyStop } = useStore('fileUpload');
        updateIsStrategyStop(true);

        // 终止当前上传任务
        if (uploadTask?.abort) uploadTask.abort();
        if (!isStrategyUploadExecuted) {
          console.log('进统一调用策略上传');
          isStrategyUploadExecuted = true;
          strategyUpload(
            filePath,
            primiFilePath,
            fileTypeNum,
            retryCount,
            formData,
            urlApi,
            resolve,
            reject,
            false, // isFirst=false，表示因策略触发
            {
              localTime: recordStartTime,
              fileSize,
              ossManagerClass,
              isOld: false,
              uploadFileSize
            },
            itemSeq,
            ossManagerClass,
            errorStr
          );
        }
      }
      eventBus.emit('unloadOnProgressUpdate', {
        progress: res.progress,
        totalBytesSent: res.totalBytesSent,
        totalBytesExpectedToSend: res.totalBytesExpectedToSend
      });
    });
    // 监听取消上传事件
    eventBus.on('cancelUpload', (res: any) => {
      console.log('ossUploadPromise---uploadFileAPI---进入阿里云上传取消---cancelUpload---', res);
      if (uploadTask && typeof uploadTask.abort === 'function') {
        try {
          uploadTask.abort();
        } catch (error) {
          console.error('终止上传任务时发生错误:', error);
        }
      }
    });
  });
};

// 策略上传
async function strategyUpload(
  filePath: string,
  primiFilePath: string,
  fileTypeNum: number,
  retryCount: number,
  formData: any,
  urlApi: string,
  resolve: any,
  reject: any,
  isfirst: boolean, // 是否是第一次改变通道
  logInfo: any,
  itemSeq?: number, // 兼容填表打卡，区分是第几个表单项的附件
  ossManagerClass?: OssManagerClassType,
  errorStr?: string
) {
  console.log(
    'strategyUpload---uploadFileAPI---进入阿里云上传，获取签名前---isfirst---',
    isfirst,
    ossManagerClass?.singleFileIdentity
  );
  const lastSingleFileIdentity = ossManagerClass?.singleFileIdentity;
  // 上传保存记录+切换通道日志
  ossManagerClass?.setSingleFileIdentity(0);
  console.log(
    'strategyUpload---uploadFileAPI---进入阿里云上传，获取签名后---singleFileIdentity---',
    ossManagerClass?.singleFileIdentity
  );
  const logFormData = {
    ...formData,
    singleFileIdentity: ossManagerClass?.singleFileIdentity,
    fileName: filePath,
    // 上传方式 1 移动云 2 阿里云 3 公司
    uploadType: 2,
    changeUploadData: {
      changeReasonCode: isfirst ? 1 : 2,
      changeParentSingleFileIdentity: lastSingleFileIdentity
    }
  };
  ossManagerClass?.saveOssUploadResultLog(logFormData, false);
  console.log(
    'strategyUpload---uploadFileAPI---进入阿里云上传，获取签名后---logFormData---',
    logFormData
  );
  // 获取oss文件上传的域签名地址
  const ossUploadPreSignedObj: any = await ossManagerClass?.getOssUploadPreSignedUrl({
    ...formData,
    fileName: filePath
  });
  // 有值表示不是链接, 走阿里云oss上传
  console.log(
    'strategyUpload---uploadFileAPI---进入阿里云上传，获取签名后---ossUploadPreSignedObj---',
    ossUploadPreSignedObj
  );
  if (ossUploadPreSignedObj && ossUploadPreSignedObj.postUrl) {
    // 走阿里云上传
    console.log(
      'strategyUpload---uploadFileAPI---进入阿里云上传，获取签名后---开始调用阿里云上传---'
    );
    ossUploadPromise(
      filePath,
      primiFilePath,
      fileTypeNum,
      retryCount,
      {
        ...formData,
        fileId: ossUploadPreSignedObj.fileId
      },
      ossUploadPreSignedObj.postUrl,
      ossUploadPreSignedObj.postData,
      itemSeq,
      ossManagerClass,
      errorStr,
      resolve,
      reject
    );
  } else {
    const res = JSON.parse(ossUploadPreSignedObj.data);
    const obj = {
      res,
      filePath,
      primiFilePath,
      fileTypeNum,
      retryCount,
      formData,
      urlApi,
      itemSeq,
      resolve,
      reject,
      logInfo
    };
    uploadFileCBSuccess(obj);
  }
}

// 阿里云oss上传
function ossUploadPromise(
  filePath: string,
  primiFilePath: string,
  fileTypeNum: number,
  retryCount: number,
  formData: any,
  urlApi: string,
  ossFormData: any,
  itemSeq?: number,
  ossManagerClass?: OssManagerClassType,
  errorStr?: string,
  resolve?: any,
  reject?: any
) {
  const uploadFileAPI = urlApi;
  const localTime = new Date();
  let fileSize = 0;
  let uploadFileSize = 0;
  const errorObj = {
    err: '阿里云上传失败',
    filePath,
    primiFilePath,
    fileTypeNum,
    retryCount,
    formData,
    urlApi,
    resolve,
    reject,
    errorStr,
    logInfo: {
      localTime,
      fileSize,
      ossManagerClass,
      isOld: false,
      uploadFileSize
    }
  };
  console.log('ossUploadPromise---uploadFileAPI---进入阿里云上传---', uploadFileAPI);
  console.log('ossUploadPromise---uploadFileAPI---进入阿里云上传---ossFormData---', ossFormData);
  console.log('ossUploadPromise---uploadFileAPI---进入阿里云上传---formData---', formData);
  const uploadTask = uni.uploadFile({
    url: uploadFileAPI,
    filePath,
    name: 'file',
    formData: ossFormData,
    // #ifdef MP-WEIXIN
    timeout: 6000000, // 超时时间600s 10分钟 取消超时实践 // 如果不设置则可能会遭到默认60s的超时时间 改100分钟
    // #endif
    success: async (resData: any) => {
      // resolve(res);
      if (resData.statusCode !== 200) {
        console.log('ossUploadPromise---success---阿里云上传失败', resData);
        uploadFileCBError(errorObj);
        // reject(resData);
      } else {
        console.log('ossUploadPromise---success---阿里云上传成功', resData);
        // resolve(resData);
        // 轮训调用 保存经过预签名的文件数据接口， 查看服务端同步阿里云文件的进度
        const res = await ossManagerClass?.loopSaveOssUploadPresignedFile({
          fileId: formData.fileId,
          fileSize
        });
        console.log('ossUploadPromise---success---阿里云上传成功---轮训调用---', res);
        if (res === 1) {
          const obj = {
            res: {
              fileId: formData.fileId
            },
            filePath,
            primiFilePath,
            fileTypeNum,
            retryCount,
            formData,
            urlApi,
            itemSeq,
            resolve,
            reject,
            logInfo: {
              localTime,
              fileSize,
              ossManagerClass,
              isOld: false,
              uploadFileSize
            }
          };
          uploadFileCBSuccess(obj);
        }
      }
    },
    fail: (err) => {
      // reject(err);
      console.log('ossUploadPromise---fail---阿里云上传失败', err);
      uploadFileCBError(errorObj);
    }
  });
  // 监听取消上传事件
  eventBus.on('cancelUpload', (res: any) => {
    console.log(
      'ossUploadPromise---uploadFileAPI---进入阿里云上传---cancelUpload---取消取消取消',
      res
    );
    if (uploadTask && typeof uploadTask.abort === 'function') {
      try {
        uploadTask.abort();
      } catch (error) {
        console.error('终止上传任务时发生错误:', error);
      }
    }
  });
  eventBus.emit('unloadOnUploadFile', {
    uploadTask
  });
  uploadTask.onProgressUpdate((res) => {
    console.log(`onProgressUpdate---阿里云---上传进度${res.progress}`);
    console.log(`onProgressUpdate---阿里云---已经上传的数据长度${res.totalBytesSent}`);
    console.log(
      `onProgressUpdate---阿里云---预期需要上传的数据总长度${res.totalBytesExpectedToSend}`
    );
    fileSize = res.totalBytesExpectedToSend;
    uploadFileSize = res.totalBytesSent;
    errorObj.logInfo.fileSize = fileSize;
    eventBus.emit('unloadOnProgressUpdate', {
      progress: res.progress,
      totalBytesSent: res.totalBytesSent,
      totalBytesExpectedToSend: res.totalBytesExpectedToSend
    });
  });
}

export { newUploadFilePromise };
