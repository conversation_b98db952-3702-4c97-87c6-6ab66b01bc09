import type { appConfigType } from './projectType';
const WxAppConfig: any = import.meta.env.VITE_LOGIN_WX_APP_CONFIG
  ? Object.fromEntries(JSON.parse(import.meta.env.VITE_LOGIN_WX_APP_CONFIG))
  : {};
const defalutAppConfig: appConfigType = {
  template: {
    basic: {
      tabBar: {
        selectedColor: '#4AD975',
        color: '#999999',
        list: [
          {
            pagePath: '/pages/home-design/new-home-design',
            text: '首页',
            iconPath: 'tabbar_app_home_unselect.png',
            selectedIconPath: 'tabbar_app_home_select.png'
          },
          {
            pagePath: '/pages/class-circles/class-circles-home',
            text: '班级圈',
            iconPath: 'tabbar_app_new_circle_unselect.png',
            selectedIconPath: 'tabbar_app_new_circle_select.png'
          },
          {
            pagePath: '/pages/mine/mine',
            text: '我的',
            iconPath: 'tabbar_app_my_unselect.png',
            selectedIconPath: 'tabbar_app_my_select.png'
          }
        ]
      },
      homeTeacherModule: [
        {
          title: '智能通知',
          icon: 'revision-home-desigin-notice',
          path: '/uni_modules/xxt-notice-uni/pages/notice-list-home/notice-list-home',
          menuId: 1,
          description: '智能通知随时发',
          bgColor: '#E6F9FE'
        },
        {
          title: '打卡',
          icon: 'revision-home-desigin-clock',
          path: '/uni_modules/xxt-clock-in-uni/pages/clock-list-home/clock-list-home',
          menuId: 4,
          description: '一键打卡',
          bgColor: '#EDF7FF'
        },
        {
          title: '成绩',
          icon: 'revision-home-desigin-score',
          path: '/uni_modules/xxt-exam-score-uni/pages/score-list-home/score-list-home',
          menuId: 2,
          description: '私密成绩',
          bgColor: '#FEF3F7'
        },
        {
          title: '作业',
          icon: 'revision-home-desigin-works',
          path: '/uni_modules/xxt-task-center-uni/pages/task-list-home/task-list-home',
          menuId: 5,
          description: '一键布置',
          bgColor: '#FFF3EA'
        }
      ],
      homeStudentModule: [
        {
          title: '智能通知',
          icon: 'revision-home-desigin-notice',
          path: '/uni_modules/xxt-notice-uni/pages/notice-list-home/notice-list-home',
          menuId: 1,
          description: '智能通知随时发',
          bgColor: '#E6F9FE'
        },
        {
          title: '打卡',
          icon: 'revision-home-desigin-clock',
          path: '/uni_modules/xxt-clock-in-uni/pages/clock-list-home/clock-list-home',
          menuId: 4,
          description: '一键打卡',
          bgColor: '#EDF7FF'
        },
        {
          title: '成绩',
          icon: 'revision-home-desigin-score',
          path: '/uni_modules/xxt-exam-score-uni/pages/score-list-home/score-list-home',
          menuId: 2,
          description: '私密成绩',
          bgColor: '#FEF3F7'
        },
        {
          title: '智能储物柜',
          icon: 'revision-home-desigin-lockers',
          path: '/uni_modules/xxt-lockers-uni/pages/lockers-home/lockers-home',
          menuId: 3,
          description: '存取更方便',
          bgColor: '#D6FAEE'
        },
        {
          title: '作业',
          icon: 'revision-home-desigin-works',
          path: '/uni_modules/xxt-task-center-uni/pages/task-list-home/task-list-home',
          menuId: 5,
          description: '一键布置',
          bgColor: '#FFF3EA'
        }
      ]
    }
  }
};
const projectConfig = {
  // 微信订阅模板id
  wxSubscribeTemplIds: {
    // 数智慧家校微信订阅老师模板id
    teacherSubscribeTemplIds: [],
    // 数智慧家校微信订阅学生模板id
    studentSubscribeTemplIds: []
  },
  // store中appConfig的默认配置，每个小程序的tabbar不一样所以单独配置
  defalutAppConfig,
  entry: 'wxmp_szjx',
  // 微信小程序账号的原始ID
  WXMPOriginalID: 'gh_9a830fd9843f',
  // 公共分包模块的配置路径
  publicSubPackgePath: `/uni_modules/uni-module-public`,
  // 主包路径
  subPackagesRoot: 'uni_modules/xxt-wxmp-uni',
  // 报名称
  subPackagesName: 'xxtWxmpUni',
  // 登录验证码
  loginPhoneCode: 71,
  // 绑定验证码
  bindPhoneCode: 54,
  // 项目id
  loginConfigKey: 'szjx',
  hostId: 102,
  // 在接口返回未登录的时候，不跳转登录页面接口白名单
  noLoginApiWhiteList: [
    'user-data-v2/user/get-user-account-list',
    'notice/common-notice/has-notice',
    'notice/common-notice/get-send-notice-list',
    'user-data-v2/auth/check-user-resource-auth'
  ]
};

export default projectConfig;
