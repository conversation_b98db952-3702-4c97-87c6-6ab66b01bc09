{
  "name": "数智家校",
  "appid": "__UNI__B2C6BE6",
  "description": "",
  "versionName": "1.0.0",
  "versionCode": 100,
  "transformPx": false,
  "networkTimeout": {
    "uploadFile": 60000,
    "downloadFile": 60000
  },
  /* 5+App特有相关 */
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "webView": {
      "minUserAgentVersion": "64.0.3282.116",
      "x5": {
        "timeOut": 3000,
        "showTipsWithoutWifi": true,
        "allowDownloadWithoutWiFi": false
      }
    },
    /* 模块配置 */
    "modules": {
      "Webview-x5": {}
    },
    /* 应用发布信息 */
    "distribute": {
      /* android打包配置 */
      "android": {
        "permissions": [
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.INTERNET\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SYNC_SETTINGS\"/>"
        ]
      },
      /* ios打包配置 */
      "ios": {},
      /* SDK配置 */
      "sdkConfigs": {}
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 数智家校小程序 */
  "mp-weixin": {
    "appid": "wx7f73222999bba2d8",
    "setting": {
      "urlCheck": false,
      "postcss": true,
      "minified": true
    },
    "usingComponents": true,
    "optimization": {
      "subpackages": true // 是否启用分包优化
    },
    // 是否开启代码按需执行。目前仅支持值 requiredComponents
    "lazyCodeLoading": "requiredComponents",
    "plugins": {
      "wmpf-voip": {
        "version": "latest", // latest 表示自动使用最新版本。也可使用具体版本，如 2.3.8
        "provider": "wxf830863afde621eb"
      }
    }
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "uniStatistics": {
    "enable": false
  },
  "vueVersion": "3"
}
