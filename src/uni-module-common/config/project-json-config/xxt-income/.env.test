# API 域名
# 1. 当构建「H5 应用」时，不需要使用此配置
# 2. 当构建「非 H5 应用」时，需要使用此配置。原因：页面与接口使用的是同一个域名，不需要特殊指定域名
# 3. 通过 uni-app 条件编译控制是否使用配置，详见 src/commons/config/index.ts
VITE_GLOB_API_URL = https://rest-test.xxt.cn

# API 接口前缀。注：不含域名部分
# 1. 当构建「H5 应用」时，不需要使用此配置
# 2. 当构建「非 H5 应用」时，需要使用此配置。原因：页面与接口使用的是同一个域名，需要通过此进行区分
# 3. 通过 uni-app 条件编译控制是否使用配置，详见 src/commons/config/index.ts
VITE_GLOB_API_URL_PREFIX = /api

# 点击日志接口域名
# 1. 当构建「H5 应用」时，不需要使用此配置
# 2. 当构建「非 H5 应用」时，需要使用此配置。原因：页面与接口使用的是同一个域名，不需要特殊指定域名
# 3. 通过 uni-app 条件编译控制是否使用配置，详见 src/commons/config/index.ts
VITE_GLOB_CLICK_URL = https://click-test.xxt.cn

# 点击日志接口前缀
VITE_GLOB_CLICK_URL_PREFIX = /click-log

# 新增域名 rest-upload.xxt.cn。新域名仅支持 /zuul 开头的请求，其他非 /zuul 请求会报错上传和下载
VITE_GLOB_UPLOAD_URL = https://rest.xxt.cn

# 静态资源 CDN 域名
VITE_GLOB_IMG_CDN_URL = https://static-test.xxt.cn

# 本地开发环境时服务暴露的端口
VITE_PORT = 8001

# 跨域接口代理
# 可以配置多个，请注意不要换行
VITE_PROXY = [["/api","http://api-gateway"],["/click-log","https://click-test.xxt.cn"]]

# 网站根目录
VITE_PUBLIC_PATH = /

# 是否开启mock
VITE_USE_MOCK = true

# 网站前缀
VITE_BASE_URL = /

# 是否删除 console
VITE_DROP_CONSOLE = false

# 在接口返回未登录的时候，不跳转登录页面接口白名单
VITE_LOGIN_WHITE_LIST = ["user-data-v2/user/get-user-account-list","notice/common-notice/has-notice","notice/common-notice/get-send-notice-list","user-data-v2/auth/check-user-resource-auth"]

# 当前登录小程序配置名称
VITE_LOGIN_WX_NAME = "xxt-income"

# 当前登录小程序各个配置 第一个是loginConfig.json配置文件的key，
# 第二个是public.json文件中subPackages的root值
VITE_LOGIN_WX_APP_CONFIG = [["loginConfigKey","xxt-income"],["subPackagesRoot","uni_modules/xxt-income-uni"],["subPackagesName","xxtIncomeUni"],["loginPhoneCode","41"],["bindPhoneCode","53"]]
