// 从微信选择附件上传后回来，告诉uni-app批次fileIdentify
export const uniEmitsWxFileIdentify = 'uniEmits-wxFileIdentify'; // uni吊起本身identify上传

export const uniEmitsOnChoosedWxFile = 'uniEmits-onChoosedWxFile'; // app端主动通信的协议调用
// 选择联系人结果
export const commonSelectUsersResult = 'commonSelectUsersResult';
// 可以开始对app选择的文件（非微信聊天会话选择的文件）进行上传 事件
export const startAPPSignFileUpload = 'startAPPSignFileUpload';
// 提交回执结果
export const submitReceiptResult = 'submitReceiptResult';
// 通知模板选择结果
export const noticeTemplateResult = 'noticeTemplateResult';
// 打卡模板选择结果
export const clockTemplateResult = 'clockTemplateResult';
