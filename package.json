{"name": "xxt-read-together-uni", "version": "1.0.0", "description": "", "author": "yuye", "scripts": {"init:submodule": "node ./script/init-submodule.js", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app:wgt": "node ./script/build-app-wgt.js", "build:app:test": "uni build -p app --mode test", "build:app:test:wgt": "node ./script/build-app-wgt.js --mode test", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "prepare": "husky install", "eslint": "eslint --ext .vue,.ts src/ --fix", "stylelint": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "lint:lint-staged": "lint-staged"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3090820231124001", "@dcloudio/uni-app-plus": "3.0.0-3090820231124001", "@dcloudio/uni-components": "3.0.0-3090820231124001", "@dcloudio/uni-h5": "3.0.0-3090820231124001", "@dcloudio/uni-mp-alipay": "3.0.0-3090820231124001", "@dcloudio/uni-mp-baidu": "3.0.0-3090820231124001", "@dcloudio/uni-mp-jd": "3.0.0-3090820231124001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3090820231124001", "@dcloudio/uni-mp-lark": "3.0.0-3090820231124001", "@dcloudio/uni-mp-qq": "3.0.0-3090820231124001", "@dcloudio/uni-mp-toutiao": "3.0.0-3090820231124001", "@dcloudio/uni-mp-weixin": "3.0.0-3090820231124001", "@dcloudio/uni-mp-xhs": "3.0.0-3090820231124001", "@dcloudio/uni-quickapp-webview": "3.0.0-3090820231124001", "@vueuse/core": "^9.7.0", "@vueuse/shared": "^9.7.0", "add": "^2.0.6", "archiver": "^7.0.1", "crypto-js": "^4.2.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lottie-miniprogram": "^1.0.12", "pinia": "2.0.33", "pinia-auto-refs": "^0.0.10", "pinia-plugin-persist-uni": "1.2.0", "uni-ajax": "^2.5.1", "uni-mini-router": "^0.1.5", "uni-read-pages-vite": "^0.0.6", "uuid": "^9.0.1", "vue": "3.3.8", "vue-demi": "*", "vue-i18n": "9.7.0"}, "devDependencies": {"@antfu/eslint-config": "^0.36.0", "@dcloudio/types": "3.4.3", "@dcloudio/uni-automator": "3.0.0-3090820231124001", "@dcloudio/uni-cli-shared": "3.0.0-3090820231124001", "@dcloudio/uni-stacktracey": "3.0.0-3090820231124001", "@dcloudio/vite-plugin-uni": "3.0.0-3090820231124001", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@uni-helper/uni-app-types": "^0.5.12", "@uni-helper/uni-ui-types": "^0.5.11", "@vue/eslint-config-typescript": "^12.0.0", "@vue/runtime-core": "3.3.8", "@vue/tsconfig": "^0.1.3", "auto-import-types": "^0.0.4", "dotenv": "^16.3.1", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "lint-staged": "^15.1.0", "postcss-html": "^1.5.0", "postcss-px2rpx": "^0.0.4", "postcss-scss": "^4.0.9", "prettier": "^3.1.0", "sass": "^1.69.5", "stylelint": "^15.11.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^5.3.1", "typescript": "^4.9.4", "unocss": "0.50.4", "unplugin-auto-import": "^0.16.7", "vite": "4.0.3", "vue-tsc": "^1.0.24"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}